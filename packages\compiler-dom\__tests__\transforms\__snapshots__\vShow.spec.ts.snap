// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: v-show transform > simple expression 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vShow: _vShow, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("div", null, null, 512 /* NEED_PATCH */)), [
      [_vShow, a]
    ])
  }
}"
`;
