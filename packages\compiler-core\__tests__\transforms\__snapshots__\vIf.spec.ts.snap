// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: v-if > codegen > basic v-if 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode } = _Vue

    return ok
      ? (_openBlock(), _createElementBlock("div", { key: 0 }))
      : _createCommentVNode("v-if", true)
  }
}"
`;

exports[`compiler: v-if > codegen > increasing key: v-if + v-else-if + v-else 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode, Fragment: _Fragment } = _Vue

    return (_openBlock(), _createElementBlock(_Fragment, null, [
      ok
        ? (_openBlock(), _createElementBlock("div", { key: 0 }))
        : (_openBlock(), _createElementBlock("p", { key: 1 })),
      another
        ? (_openBlock(), _createElementBlock("div", { key: 2 }))
        : orNot
          ? (_openBlock(), _createElementBlock("p", { key: 3 }))
          : (_openBlock(), _createElementBlock("p", { key: 4 }))
    ], 64 /* STABLE_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-if > codegen > multiple v-if that are sibling nodes should have different keys 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode, Fragment: _Fragment } = _Vue

    return (_openBlock(), _createElementBlock(_Fragment, null, [
      ok
        ? (_openBlock(), _createElementBlock("div", { key: 0 }))
        : _createCommentVNode("v-if", true),
      orNot
        ? (_openBlock(), _createElementBlock("p", { key: 1 }))
        : _createCommentVNode("v-if", true)
    ], 64 /* STABLE_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-if > codegen > template v-if 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createElementVNode: _createElementVNode, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode } = _Vue

    return ok
      ? (_openBlock(), _createElementBlock(_Fragment, { key: 0 }, [
          _createElementVNode("div"),
          "hello",
          _createElementVNode("p")
        ], 64 /* STABLE_FRAGMENT */))
      : _createCommentVNode("v-if", true)
  }
}"
`;

exports[`compiler: v-if > codegen > template v-if w/ single <slot/> child 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderSlot: _renderSlot, createCommentVNode: _createCommentVNode } = _Vue

    return ok
      ? _renderSlot($slots, "default", { key: 0 })
      : _createCommentVNode("v-if", true)
  }
}"
`;

exports[`compiler: v-if > codegen > v-if + v-else 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode } = _Vue

    return ok
      ? (_openBlock(), _createElementBlock("div", { key: 0 }))
      : (_openBlock(), _createElementBlock("p", { key: 1 }))
  }
}"
`;

exports[`compiler: v-if > codegen > v-if + v-else-if + v-else 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode, Fragment: _Fragment } = _Vue

    return ok
      ? (_openBlock(), _createElementBlock("div", { key: 0 }))
      : orNot
        ? (_openBlock(), _createElementBlock("p", { key: 1 }))
        : (_openBlock(), _createElementBlock(_Fragment, { key: 2 }, ["fine"], 64 /* STABLE_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-if > codegen > v-if + v-else-if 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode } = _Vue

    return ok
      ? (_openBlock(), _createElementBlock("div", { key: 0 }))
      : orNot
        ? (_openBlock(), _createElementBlock("p", { key: 1 }))
        : _createCommentVNode("v-if", true)
  }
}"
`;

exports[`compiler: v-if > codegen > v-if on <slot/> 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderSlot: _renderSlot, createCommentVNode: _createCommentVNode } = _Vue

    return ok
      ? _renderSlot($slots, "default", { key: 0 })
      : _createCommentVNode("v-if", true)
  }
}"
`;
