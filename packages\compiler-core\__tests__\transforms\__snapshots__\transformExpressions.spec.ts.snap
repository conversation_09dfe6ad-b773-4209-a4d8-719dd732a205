// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: expression transform > bindingMetadata > inline mode 1`] = `
"(_ctx, _cache) => {
  return (_openBlock(), _createElementBlock("div", null, _toDisplayString(__props.props) + " " + _toDisplayString(_unref(setup)) + " " + _toDisplayString(setupConst) + " " + _toDisplayString(_ctx.data) + " " + _toDisplayString(_ctx.options) + " " + _toDisplayString(isNaN.value), 1 /* TEXT */))
}"
`;

exports[`compiler: expression transform > bindingMetadata > non-inline mode 1`] = `
"const { toDisplayString: _toDisplayString, openBlock: _openBlock, createElementBlock: _createElementBlock } = Vue

return function render(_ctx, _cache, $props, $setup, $data, $options) {
  return (_openBlock(), _createElementBlock("div", null, _toDisplayString($props.props) + " " + _toDisplayString($setup.setup) + " " + _toDisplayString($data.data) + " " + _toDisplayString($options.options) + " " + _toDisplayString($setup.isNaN), 1 /* TEXT */))
}"
`;

exports[`compiler: expression transform > bindingMetadata > should not prefix temp variable of for loop 1`] = `
"const { openBlock: _openBlock, createElementBlock: _createElementBlock } = Vue

return function render(_ctx, _cache, $props, $setup, $data, $options) {
  return (_openBlock(), _createElementBlock("div", {
    onClick: () => {
          for (let i = 0; i < _ctx.list.length; i++) {
            _ctx.log(i)
          }
        }
  }, null, 8 /* PROPS */, ["onClick"]))
}"
`;

exports[`compiler: expression transform > bindingMetadata > should not prefix temp variable of for...in 1`] = `
"const { openBlock: _openBlock, createElementBlock: _createElementBlock } = Vue

return function render(_ctx, _cache, $props, $setup, $data, $options) {
  return (_openBlock(), _createElementBlock("div", {
    onClick: () => {
          for (const x in _ctx.list) {
            _ctx.log(x)
          }
        }
  }, null, 8 /* PROPS */, ["onClick"]))
}"
`;

exports[`compiler: expression transform > bindingMetadata > should not prefix temp variable of for...of 1`] = `
"const { openBlock: _openBlock, createElementBlock: _createElementBlock } = Vue

return function render(_ctx, _cache, $props, $setup, $data, $options) {
  return (_openBlock(), _createElementBlock("div", {
    onClick: () => {
          for (const x of _ctx.list) {
            _ctx.log(x)
          }
        }
  }, null, 8 /* PROPS */, ["onClick"]))
}"
`;
