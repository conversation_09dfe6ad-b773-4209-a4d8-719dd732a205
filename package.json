{"private": true, "version": "3.4.21", "packageManager": "pnpm@8.15.5", "type": "module", "scripts": {"dev": "node scripts/dev.js", "build": "node scripts/build.js", "build-dts": "tsc -p tsconfig.build.json && rollup -c rollup.dts.config.js", "clean": "rimraf packages/*/dist temp .eslintcache", "size": "run-s \"size-*\" && tsx scripts/usage-size.ts", "size-global": "node scripts/build.js vue runtime-dom -f global -p --size", "size-esm-runtime": "node scripts/build.js vue -f esm-bundler-runtime", "size-esm": "node scripts/build.js runtime-dom runtime-core reactivity shared -f esm-bundler", "check": "tsc --incremental --noEmit", "lint": "eslint --cache --ext .js,.ts,.tsx . --ignore-path .gitignore", "format": "prettier --write --cache .", "format-check": "prettier --check --cache .", "test": "vitest", "test-unit": "vitest -c vitest.unit.config.ts", "test-e2e": "node scripts/build.js vue -f global -d && vitest -c vitest.e2e.config.ts", "test-dts": "run-s build-dts test-dts-only", "test-dts-only": "tsc -p ./packages/dts-test/tsconfig.test.json", "test-coverage": "vitest -c vitest.unit.config.ts --coverage", "test-bench": "vitest bench", "release": "node scripts/release.js", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "dev-esm": "node scripts/dev.js -if esm-bundler-runtime", "dev-compiler": "run-p \"dev template-explorer\" serve", "dev-sfc": "run-s dev-sfc-prepare dev-sfc-run", "dev-sfc-prepare": "node scripts/pre-dev-sfc.js || npm run build-all-cjs", "dev-sfc-serve": "vite packages/sfc-playground --host", "dev-sfc-run": "run-p \"dev compiler-sfc -f esm-browser\" \"dev vue -if esm-bundler-runtime\" \"dev vue -ipf esm-browser-runtime\" \"dev server-renderer -if esm-bundler\" dev-sfc-serve", "serve": "serve", "open": "open http://localhost:3000/packages/template-explorer/local.html", "build-sfc-playground": "run-s build-all-cjs build-runtime-esm build-browser-esm build-ssr-esm build-sfc-playground-self", "build-all-cjs": "node scripts/build.js vue runtime compiler reactivity shared -af cjs", "build-runtime-esm": "node scripts/build.js runtime reactivity shared -af esm-bundler && node scripts/build.js vue -f esm-bundler-runtime && node scripts/build.js vue -f esm-browser-runtime", "build-browser-esm": "node scripts/build.js runtime reactivity shared -af esm-bundler && node scripts/build.js vue -f esm-bundler && node scripts/build.js vue -f esm-browser", "build-ssr-esm": "node scripts/build.js compiler-sfc server-renderer -f esm-browser", "build-sfc-playground-self": "cd packages/sfc-playground && npm run build", "preinstall": "npx only-allow pnpm", "postinstall": "simple-git-hooks"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged && pnpm check", "commit-msg": "node scripts/verify-commit.js"}, "lint-staged": {"*.{js,json}": ["prettier --write"], "*.ts?(x)": ["eslint --fix", "prettier --parser=typescript --write"]}, "engines": {"node": ">=18.12.0"}, "devDependencies": {"@babel/parser": "^7.24.1", "@babel/types": "^7.24.0", "@codspeed/vitest-plugin": "^3.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "5.0.4", "@rollup/plugin-terser": "^0.4.4", "@types/hash-sum": "^1.0.2", "@types/minimist": "^1.2.5", "@types/node": "^20.12.2", "@types/semver": "^7.5.8", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "@vitest/coverage-istanbul": "^1.4.0", "@vue/consolidate": "1.0.0", "conventional-changelog-cli": "^4.1.0", "enquirer": "^2.4.1", "esbuild": "^0.20.2", "esbuild-plugin-polyfill-node": "^0.3.0", "eslint": "^8.57.0", "eslint-define-config": "^2.1.0", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "eslint-plugin-jest": "^27.9.0", "estree-walker": "^2.0.2", "execa": "^8.0.1", "jsdom": "^24.0.0", "lint-staged": "^15.2.2", "lodash": "^4.17.21", "magic-string": "^0.30.8", "markdown-table": "^3.0.3", "marked": "^12.0.1", "minimist": "^1.2.8", "npm-run-all2": "^6.1.2", "picocolors": "^1.0.0", "prettier": "^3.2.5", "pretty-bytes": "^6.1.1", "pug": "^3.0.2", "puppeteer": "~22.6.1", "rimraf": "^5.0.5", "rollup": "^4.13.2", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-esbuild": "^6.1.1", "rollup-plugin-polyfill-node": "^0.13.0", "semver": "^7.6.0", "serve": "^14.2.1", "simple-git-hooks": "^2.11.1", "terser": "^5.30.1", "todomvc-app-css": "^2.4.3", "tslib": "^2.6.2", "tsx": "^4.7.1", "typescript": "^5.2.2", "vite": "^5.2.7", "vitest": "^1.4.0"}}