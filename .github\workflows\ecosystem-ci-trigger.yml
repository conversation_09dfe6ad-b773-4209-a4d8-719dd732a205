name: ecosystem-ci trigger

on:
  issue_comment:
    types: [created]

jobs:
  trigger:
    runs-on: ubuntu-latest
    if: github.repository == 'vuejs/core' && github.event.issue.pull_request && startsWith(github.event.comment.body, '/ecosystem-ci run')
    steps:
      - uses: actions/github-script@v7
        with:
          script: |
            const user = context.payload.sender.login
            console.log(`Validate user: ${user}`)

            let isVuejsMember = false
            try {
              const { status } = await github.rest.orgs.checkMembershipForUser({
                org: 'vuejs',
                username: user
              });

              isVuejsMember = (status === 204)
            } catch (e) {}

            if (isVuejsMember) {
              console.log('Allowed')
              await github.rest.reactions.createForIssueComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: context.payload.comment.id,
                content: '+1',
              })
            } else {
              console.log('Not allowed')
              await github.rest.reactions.createForIssueComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: context.payload.comment.id,
                content: '-1',
              })
              throw new Error('not allowed')
            }
      - uses: actions/github-script@v7
        id: get-pr-data
        with:
          script: |
            console.log(`Get PR info: ${context.repo.owner}/${context.repo.repo}#${context.issue.number}`)
            const { data: pr } = await github.rest.pulls.get({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number
            })
            return {
              num: context.issue.number,
              branchName: pr.head.ref,
              repo: pr.head.repo.full_name
            }
      - uses: actions/github-script@v7
        id: trigger
        env:
          COMMENT: ${{ github.event.comment.body }}
        with:
          github-token: ${{ secrets.ECOSYSTEM_CI_ACCESS_TOKEN }}
          result-encoding: string
          script: |
            const comment = process.env.COMMENT.trim()
            const prData = ${{ steps.get-pr-data.outputs.result }}

            const suite = comment.replace(/^\/ecosystem-ci run/, '').trim()

            await github.rest.actions.createWorkflowDispatch({
              owner: context.repo.owner,
              repo: 'ecosystem-ci',
              workflow_id: 'ecosystem-ci-from-pr.yml',
              ref: 'main',
              inputs: {
                prNumber: '' + prData.num,
                branchName: prData.branchName,
                repo: prData.repo,
                suite: suite === '' ? '-' : suite
              }
            })
