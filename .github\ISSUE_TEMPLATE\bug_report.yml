name: "\U0001F41E Bug report"
description: Create a report to help us improve
body:
  - type: markdown
    attributes:
      value: |
        **Before You Start...**

        This form is only for submitting bug reports. If you have a usage question
        or are unsure if this is really a bug, make sure to:

        - Read the [docs](https://vuejs.org/)
        - Ask on [<PERSON><PERSON>t](https://chat.vuejs.org/)
        - Ask on [GitHub Discussions](https://github.com/vuejs/core/discussions)
        - Look for / ask questions on [Stack Overflow](https://stackoverflow.com/questions/ask?tags=vue.js)

        Also try to search for your issue - it may have already been answered or even fixed in the development branch.
        However, if you find that an old, closed issue still persists in the latest version,
        you should open a new issue using the form below instead of commenting on the old issue.
  - type: input
    id: version
    attributes:
      label: Vue version
    validations:
      required: true
  - type: input
    id: reproduction-link
    attributes:
      label: Link to minimal reproduction
      description: |
        The easiest way to provide a reproduction is by showing the bug in [The SFC Playground](https://play.vuejs.org/).
        If it cannot be reproduced in the playground and requires a proper build setup, try [StackBlitz](https://vite.new/vue).
        If neither of these are suitable, you can always provide a GitHub repository.

        The reproduction should be **minimal** - i.e. it should contain only the bare minimum amount of code needed
        to show the bug. See [Bug Reproduction Guidelines](https://github.com/vuejs/core/blob/main/.github/bug-repro-guidelines.md) for more details.

        Please do not just fill in a random link. The issue will be closed if no valid reproduction is provided.
      placeholder: Reproduction Link
    validations:
      required: true
  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to reproduce
      description: |
        What do we need to do after opening your repro in order to make the bug happen? Clear and concise reproduction instructions are important for us to be able to triage your issue in a timely manner. Note that you can use [Markdown](https://guides.github.com/features/mastering-markdown/) to format lists and code.
      placeholder: Steps to reproduce
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: What is expected?
    validations:
      required: true
  - type: textarea
    id: actually-happening
    attributes:
      label: What is actually happening?
    validations:
      required: true
  - type: textarea
    id: system-info
    attributes:
      label: System Info
      description: Output of `npx envinfo --system --npmPackages vue --binaries --browsers`
      render: shell
      placeholder: System, Binaries, Browsers
  - type: textarea
    id: additional-comments
    attributes:
      label: Any additional comments?
      description: e.g. some background/context of how you ran into this bug.
