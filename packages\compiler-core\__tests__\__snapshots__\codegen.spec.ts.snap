// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: codegen > ArrayExpression 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    return [
      foo,
      bar(baz)
    ]
  }
}"
`;

exports[`compiler: codegen > CacheExpression 1`] = `
"
export function render(_ctx, _cache) {
  return _cache[1] || (_cache[1] = foo)
}"
`;

exports[`compiler: codegen > CacheExpression w/ isVNode: true 1`] = `
"
export function render(_ctx, _cache) {
  return _cache[1] || (
    _setBlockTracking(-1),
    _cache[1] = foo,
    _setBlockTracking(1),
    _cache[1]
  )
}"
`;

exports[`compiler: codegen > ConditionalExpression 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    return ok
      ? foo()
      : orNot
        ? bar()
        : baz()
  }
}"
`;

exports[`compiler: codegen > Element (callExpression + objectExpression + TemplateChildNode[]) 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    return _createElementVNode("div", {
      id: "foo",
      [prop]: bar,
      [foo + bar]: bar
    }, [
      _createElementVNode("p", { "some-key": "foo" })
    ], 16)
  }
}"
`;

exports[`compiler: codegen > assets + temps 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    const _component_Foo = _resolveComponent("Foo")
    const _component_bar_baz = _resolveComponent("bar-baz")
    const _component_barbaz = _resolveComponent("barbaz")
    const _component_Qux = _resolveComponent("Qux", true)
    const _directive_my_dir_0 = _resolveDirective("my_dir_0")
    const _directive_my_dir_1 = _resolveDirective("my_dir_1")
    let _temp0, _temp1, _temp2

    return null
  }
}"
`;

exports[`compiler: codegen > comment 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    return _createCommentVNode("foo")
  }
}"
`;

exports[`compiler: codegen > compound expression 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    return _ctx.foo + _toDisplayString(bar) + nested
  }
}"
`;

exports[`compiler: codegen > forNode 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(), 1))
  }
}"
`;

exports[`compiler: codegen > forNode with constant expression 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    return (_openBlock(), _createElementBlock(_Fragment, null, _renderList(), 64 /* STABLE_FRAGMENT */))
  }
}"
`;

exports[`compiler: codegen > function mode preamble 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createVNode: _createVNode, resolveDirective: _resolveDirective } = _Vue

    return null
  }
}"
`;

exports[`compiler: codegen > function mode preamble w/ prefixIdentifiers: true 1`] = `
"const { createVNode: _createVNode, resolveDirective: _resolveDirective } = Vue

return function render(_ctx, _cache) {
  return null
}"
`;

exports[`compiler: codegen > hoists 1`] = `
"
const _hoisted_1 = hello
const _hoisted_2 = { id: "foo" }

return function render(_ctx, _cache) {
  with (_ctx) {
    return null
  }
}"
`;

exports[`compiler: codegen > ifNode 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    return foo
      ? bar
      : baz
  }
}"
`;

exports[`compiler: codegen > interpolation 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    return _toDisplayString(hello)
  }
}"
`;

exports[`compiler: codegen > module mode preamble 1`] = `
"import { createVNode as _createVNode, resolveDirective as _resolveDirective } from "vue"

export function render(_ctx, _cache) {
  return null
}"
`;

exports[`compiler: codegen > module mode preamble w/ optimizeImports: true 1`] = `
"import { createVNode, resolveDirective } from "vue"

// Binding optimization for webpack code-split
const _createVNode = createVNode, _resolveDirective = resolveDirective

export function render(_ctx, _cache) {
  return null
}"
`;

exports[`compiler: codegen > static text 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    return "hello"
  }
}"
`;

exports[`compiler: codegen > temps 1`] = `
"
return function render(_ctx, _cache) {
  with (_ctx) {
    let _temp0, _temp1, _temp2

    return null
  }
}"
`;
