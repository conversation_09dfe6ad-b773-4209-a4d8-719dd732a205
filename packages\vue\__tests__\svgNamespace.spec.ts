// SVG logic is technically dom-specific, but the logic is placed in core
// because splitting it out of core would lead to unnecessary complexity in both
// the renderer and compiler implementations.
// Related files:
// - runtime-core/src/renderer.ts
// - compiler-core/src/transforms/transformElement.ts

import { vtc<PERSON>ey } from '../../runtime-dom/src/components/Transition'
import { h, nextTick, ref, render } from '../src'

describe('SVG support', () => {
  afterEach(() => {
    document.body.innerHTML = ''
  })

  test('should mount elements with correct html namespace', () => {
    const root = document.createElement('div')
    document.body.appendChild(root)
    const App = {
      template: `
        <div id="e0">
          <svg id="e1">
            <foreignObject id="e2">
              <div id="e3"/>
              <svg id="e4"/>
              <math id="e5"/>
            </foreignObject>
          </svg>
        </div>
      `,
    }
    render(h(App), root)
    const e0 = document.getElementById('e0')!
    expect(e0.namespaceURI).toMatch('xhtml')
    expect(e0.querySelector('#e1')!.namespaceURI).toMatch('svg')
    expect(e0.querySelector('#e2')!.namespaceURI).toMatch('svg')
    expect(e0.querySelector('#e3')!.namespaceURI).toMatch('xhtml')
    expect(e0.querySelector('#e4')!.namespaceURI).toMatch('svg')
    expect(e0.querySelector('#e5')!.namespaceURI).toMatch('Math')
  })

  test('should patch elements with correct namespaces', async () => {
    const root = document.createElement('div')
    document.body.appendChild(root)
    const cls = ref('foo')
    const App = {
      setup: () => ({ cls }),
      template: `
        <div>
          <svg id="f1" :class="cls">
            <foreignObject>
              <div id="f2" :class="cls"/>
            </foreignObject>
          </svg>
        </div>
      `,
    }
    render(h(App), root)
    const f1 = document.querySelector('#f1')!
    const f2 = document.querySelector('#f2')!
    expect(f1.getAttribute('class')).toBe('foo')
    expect(f2.className).toBe('foo')

    // set a transition class on the <div> - which is only respected on non-svg
    // patches
    ;(f2 as any)[vtcKey] = ['baz']
    cls.value = 'bar'
    await nextTick()
    expect(f1.getAttribute('class')).toBe('bar')
    expect(f2.className).toBe('bar baz')
  })
})
