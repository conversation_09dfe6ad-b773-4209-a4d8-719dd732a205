// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`SFC analyze <script> bindings > auto name inference > basic 1`] = `
"const a = 1
export default {
  __name: 'FooBar',
  setup(__props, { expose: __expose }) {
  __expose();

return { a }
}

}"
`;

exports[`SFC analyze <script> bindings > auto name inference > do not overwrite manual name (call) 1`] = `
"
        import { defineComponent } from 'vue'
        const __default__ = defineComponent({
          name: '<PERSON><PERSON>'
        })
        
export default /*#__PURE__*/Object.assign(__default__, {
  setup(__props, { expose: __expose }) {
  __expose();
const a = 1
return { a, defineComponent }
}

})"
`;

exports[`SFC analyze <script> bindings > auto name inference > do not overwrite manual name (object) 1`] = `
"
        const __default__ = {
          name: 'Baz'
        }
        
export default /*#__PURE__*/Object.assign(__default__, {
  setup(__props, { expose: __expose }) {
  __expose();
const a = 1
return { a }
}

})"
`;

exports[`SFC compile <script setup> > <script> and <script setup> co-usage > export call expression as default 1`] = `
"
      function fn() {
        return "hello, world";
      }
      const __default__ = fn();
      
export default /*#__PURE__*/Object.assign(__default__, {
  setup(__props, { expose: __expose }) {
  __expose();

      console.log('foo')
      
return { fn }
}

})"
`;

exports[`SFC compile <script setup> > <script> and <script setup> co-usage > keep original semi style 1`] = `
"
export default {
  props: ['item'],
  emits: ['change'],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

        console.log('test')
        const props = __props;
        const emit = __emit;
        (function () {})()
        
return { props, emit }
}

}"
`;

exports[`SFC compile <script setup> > <script> and <script setup> co-usage > script first 1`] = `
"import { x } from './x'
      
      export const n = 1

      const __default__ = {}
      
export default /*#__PURE__*/Object.assign(__default__, {
  setup(__props, { expose: __expose }) {
  __expose();

      x()
      
return { n, get x() { return x } }
}

})"
`;

exports[`SFC compile <script setup> > <script> and <script setup> co-usage > script setup first 1`] = `
"import { x } from './x'
      
      export const n = 1
      const __default__ = {}
      

export default /*#__PURE__*/Object.assign(__default__, {
  setup(__props, { expose: __expose }) {
  __expose();

      x()
      
return { n, get x() { return x } }
}

})"
`;

exports[`SFC compile <script setup> > <script> and <script setup> co-usage > script setup first, lang="ts", script block content export default 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { x } from './x'
      
      const __default__ = {
        name: "test"
      }
      

export default /*#__PURE__*/_defineComponent({
  ...__default__,
  setup(__props, { expose: __expose }) {
  __expose();

      x()
      
return { get x() { return x } }
}

})"
`;

exports[`SFC compile <script setup> > <script> and <script setup> co-usage > script setup first, named default export 1`] = `
"import { x } from './x'
      
      export const n = 1
      const def = {}
      
      
const __default__ = def


export default /*#__PURE__*/Object.assign(__default__, {
  setup(__props, { expose: __expose }) {
  __expose();

      x()
      
return { n, def, get x() { return x } }
}

})"
`;

exports[`SFC compile <script setup> > <script> and <script setup> co-usage > spaces in ExportDefaultDeclaration node > with many spaces and newline 1`] = `
"import { x } from './x'
        
        export const n = 1
        const __default__ = {
          some:'option'
        }
        
export default /*#__PURE__*/Object.assign(__default__, {
  setup(__props, { expose: __expose }) {
  __expose();

        x()
        
return { n, get x() { return x } }
}

})"
`;

exports[`SFC compile <script setup> > <script> and <script setup> co-usage > spaces in ExportDefaultDeclaration node > with minimal spaces 1`] = `
"import { x } from './x'
        
        export const n = 1
        const __default__ = {
          some:'option'
        }
        
export default /*#__PURE__*/Object.assign(__default__, {
  setup(__props, { expose: __expose }) {
  __expose();

        x()
        
return { n, get x() { return x } }
}

})"
`;

exports[`SFC compile <script setup> > async/await detection > expression statement 1`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
;(
  ([__temp,__restore] = _withAsyncContext(() => foo)),
  await __temp,
  __restore()
)
return {  }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > multiple \`if for\` nested statements 1`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
if (ok) {
        for (let a of [1,2,3]) {
          (
  ([__temp,__restore] = _withAsyncContext(() => a)),
  await __temp,
  __restore()
)
        }
        for (let a of [1,2,3]) {
          (
  ([__temp,__restore] = _withAsyncContext(() => a)),
  await __temp,
  __restore()
)
          ;(
  ([__temp,__restore] = _withAsyncContext(() => a)),
  await __temp,
  __restore()
)
        }
      }
return {  }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > multiple \`if while\` nested statements 1`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
if (ok) {
        while (d) {
          (
  ([__temp,__restore] = _withAsyncContext(() => 5)),
  await __temp,
  __restore()
)
        }
        while (d) {
          (
  ([__temp,__restore] = _withAsyncContext(() => 5)),
  await __temp,
  __restore()
)
          ;(
  ([__temp,__restore] = _withAsyncContext(() => 6)),
  await __temp,
  __restore()
)
          if (c) {
            let f = 10
            10 + (
  ([__temp,__restore] = _withAsyncContext(() => 7)),
  __temp = await __temp,
  __restore(),
  __temp
)
          } else {
            (
  ([__temp,__restore] = _withAsyncContext(() => 8)),
  await __temp,
  __restore()
)
            ;(
  ([__temp,__restore] = _withAsyncContext(() => 9)),
  await __temp,
  __restore()
)
          }
        }
      }
return {  }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > multiple \`if\` nested statements 1`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
if (ok) {
        let a = 'foo'
        ;(
  ([__temp,__restore] = _withAsyncContext(() => 0)),
  __temp = await __temp,
  __restore(),
  __temp
) + (
  ([__temp,__restore] = _withAsyncContext(() => 1)),
  __temp = await __temp,
  __restore(),
  __temp
)
        ;(
  ([__temp,__restore] = _withAsyncContext(() => 2)),
  await __temp,
  __restore()
)
      } else if (a) {
        (
  ([__temp,__restore] = _withAsyncContext(() => 10)),
  await __temp,
  __restore()
)
        if (b) {
          (
  ([__temp,__restore] = _withAsyncContext(() => 0)),
  __temp = await __temp,
  __restore(),
  __temp
) + (
  ([__temp,__restore] = _withAsyncContext(() => 1)),
  __temp = await __temp,
  __restore(),
  __temp
)
        } else {
          let a = 'foo'
          ;(
  ([__temp,__restore] = _withAsyncContext(() => 2)),
  await __temp,
  __restore()
)
        }
        if (b) {
          (
  ([__temp,__restore] = _withAsyncContext(() => 3)),
  await __temp,
  __restore()
)
          ;(
  ([__temp,__restore] = _withAsyncContext(() => 4)),
  await __temp,
  __restore()
)
        }
      } else {
        (
  ([__temp,__restore] = _withAsyncContext(() => 5)),
  await __temp,
  __restore()
)
      }
return {  }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > nested await 1`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
;(
  ([__temp,__restore] = _withAsyncContext(async () => ((
  ([__temp,__restore] = _withAsyncContext(() => foo)),
  __temp = await __temp,
  __restore(),
  __temp
)))),
  await __temp,
  __restore()
)
return {  }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > nested await 2`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
;(
  ([__temp,__restore] = _withAsyncContext(async () => (((
  ([__temp,__restore] = _withAsyncContext(() => foo)),
  __temp = await __temp,
  __restore(),
  __temp
))))),
  await __temp,
  __restore()
)
return {  }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > nested await 3`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
;(
  ([__temp,__restore] = _withAsyncContext(async () => ((
  ([__temp,__restore] = _withAsyncContext(async () => ((
  ([__temp,__restore] = _withAsyncContext(() => foo)),
  __temp = await __temp,
  __restore(),
  __temp
)))),
  __temp = await __temp,
  __restore(),
  __temp
)))),
  await __temp,
  __restore()
)
return {  }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > nested leading await in expression statement 1`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
foo()
;(
  ([__temp,__restore] = _withAsyncContext(() => 1)),
  __temp = await __temp,
  __restore(),
  __temp
) + (
  ([__temp,__restore] = _withAsyncContext(() => 2)),
  __temp = await __temp,
  __restore(),
  __temp
)
return {  }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > nested statements 1`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
if (ok) { (
  ([__temp,__restore] = _withAsyncContext(() => foo)),
  await __temp,
  __restore()
) } else { (
  ([__temp,__restore] = _withAsyncContext(() => bar)),
  await __temp,
  __restore()
) }
return {  }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > ref 1`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
let a = ref(1 + ((
  ([__temp,__restore] = _withAsyncContext(() => foo)),
  __temp = await __temp,
  __restore(),
  __temp
)))
return { get a() { return a }, set a(v) { a = v } }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > should ignore await inside functions 1`] = `
"
export default {
  setup(__props, { expose: __expose }) {
  __expose();
async function foo() { await bar }
return { foo }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > should ignore await inside functions 2`] = `
"
export default {
  setup(__props, { expose: __expose }) {
  __expose();
const foo = async () => { await bar }
return { foo }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > should ignore await inside functions 3`] = `
"
export default {
  setup(__props, { expose: __expose }) {
  __expose();
const obj = { async method() { await bar }}
return { obj }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > should ignore await inside functions 4`] = `
"
export default {
  setup(__props, { expose: __expose }) {
  __expose();
const cls = class Foo { async method() { await bar }}
return { cls }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > single line conditions 1`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
if (false) (
  ([__temp,__restore] = _withAsyncContext(() => foo())),
  await __temp,
  __restore()
)
return {  }
}

}"
`;

exports[`SFC compile <script setup> > async/await detection > variable 1`] = `
"import { withAsyncContext as _withAsyncContext } from 'vue'

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore
const a = 1 + ((
  ([__temp,__restore] = _withAsyncContext(() => foo)),
  __temp = await __temp,
  __restore(),
  __temp
))
return { a }
}

}"
`;

exports[`SFC compile <script setup> > binding analysis for destructure 1`] = `
"
export default {
  setup(__props, { expose: __expose }) {
  __expose();

      const { foo, b: bar, ['x' + 'y']: baz, x: { y, zz: { z }}} = {}
      
return { foo, bar, baz, y, z }
}

}"
`;

exports[`SFC compile <script setup> > errors > should allow defineProps/Emit() referencing imported binding 1`] = `
"import { bar } from './bar'
        
export default {
  props: {
          foo: {
            default: () => bar
          }
        },
  emits: {
          foo: () => bar > 1
        },
  setup(__props, { expose: __expose }) {
  __expose();

        
        
        
return { get bar() { return bar } }
}

}"
`;

exports[`SFC compile <script setup> > errors > should allow defineProps/Emit() referencing scope var 1`] = `
"const bar = 1
          
export default {
  props: {
            foo: {
              default: bar => bar + 1
            }
          },
  emits: {
            foo: bar => bar > 1
          },
  setup(__props, { expose: __expose }) {
  __expose();

          
          
        
return { bar }
}

}"
`;

exports[`SFC compile <script setup> > imports > dedupe between user & helper 1`] = `
"import { useCssVars as _useCssVars, unref as _unref } from 'vue'
import { useCssVars, ref } from 'vue'
      
export default {
  setup(__props, { expose: __expose }) {
  __expose();

_useCssVars(_ctx => ({
  "xxxxxxxx-msg": (msg.value)
}))

      const msg = ref()
      
return { msg, useCssVars, ref }
}

}"
`;

exports[`SFC compile <script setup> > imports > import dedupe between <script> and <script setup> 1`] = `
"
        
        import { x } from './x'
        
export default {
  setup(__props, { expose: __expose }) {
  __expose();

        x()
        
return { get x() { return x } }
}

}"
`;

exports[`SFC compile <script setup> > imports > should allow defineProps/Emit at the start of imports 1`] = `
"import { ref } from 'vue'
      
export default {
  props: ['foo'],
  emits: ['bar'],
  setup(__props, { expose: __expose }) {
  __expose();

      
      
      const r = ref(0)
      
return { r, ref }
}

}"
`;

exports[`SFC compile <script setup> > imports > should extract comment for import or type declarations 1`] = `
"import a from 'a' // comment
        import b from 'b'
        
export default {
  setup(__props, { expose: __expose }) {
  __expose();

        
return { get a() { return a }, get b() { return b } }
}

}"
`;

exports[`SFC compile <script setup> > imports > should hoist and expose imports 1`] = `
"import { ref } from 'vue'
          import 'foo/css'
        
export default {
  setup(__props, { expose: __expose }) {
  __expose();

          
return { ref }
}

}"
`;

exports[`SFC compile <script setup> > imports > should support module string names syntax 1`] = `
"
      
        import { "😏" as foo } from './foo'
      
export default {
  setup(__props, { expose: __expose }) {
  __expose();

        
return { get foo() { return foo } }
}

}"
`;

exports[`SFC compile <script setup> > inlineTemplate mode > avoid unref() when necessary 1`] = `
"import { unref as _unref, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

import { ref } from 'vue'
        import Foo, { bar } from './Foo.vue'
        import other from './util'
        import * as tree from './tree'
        
export default {
  setup(__props) {

        const count = ref(0)
        const constant = {}
        const maybe = foo()
        let lett = 1
        function fn() {}
        
return (_ctx, _cache) => {
  return (_openBlock(), _createElementBlock(_Fragment, null, [
    _createVNode(Foo, null, {
      default: _withCtx(() => [
        _createTextVNode(_toDisplayString(_unref(bar)), 1 /* TEXT */)
      ]),
      _: 1 /* STABLE */
    }),
    _createElementVNode("div", { onClick: fn }, _toDisplayString(count.value) + " " + _toDisplayString(constant) + " " + _toDisplayString(_unref(maybe)) + " " + _toDisplayString(_unref(lett)) + " " + _toDisplayString(_unref(other)), 1 /* TEXT */),
    _createTextVNode(" " + _toDisplayString(tree.foo()), 1 /* TEXT */)
  ], 64 /* STABLE_FRAGMENT */))
}
}

}"
`;

exports[`SFC compile <script setup> > inlineTemplate mode > referencing scope components and directives 1`] = `
"import { unref as _unref, createElementVNode as _createElementVNode, withDirectives as _withDirectives, createVNode as _createVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

import ChildComp from './Child.vue'
        import SomeOtherComp from './Other.vue'
        import vMyDir from './my-dir'
        
export default {
  setup(__props) {

        
return (_ctx, _cache) => {
  return (_openBlock(), _createElementBlock(_Fragment, null, [
    _withDirectives(_createElementVNode("div", null, null, 512 /* NEED_PATCH */), [
      [_unref(vMyDir)]
    ]),
    _createVNode(ChildComp),
    _createVNode(SomeOtherComp)
  ], 64 /* STABLE_FRAGMENT */))
}
}

}"
`;

exports[`SFC compile <script setup> > inlineTemplate mode > should work 1`] = `
"import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

const _hoisted_1 = /*#__PURE__*/_createElementVNode("div", null, "static", -1 /* HOISTED */)

import { ref } from 'vue'
        
export default {
  setup(__props) {

        const count = ref(0)
        
return (_ctx, _cache) => {
  return (_openBlock(), _createElementBlock(_Fragment, null, [
    _createElementVNode("div", null, _toDisplayString(count.value), 1 /* TEXT */),
    _hoisted_1
  ], 64 /* STABLE_FRAGMENT */))
}
}

}"
`;

exports[`SFC compile <script setup> > inlineTemplate mode > ssr codegen 1`] = `
"import { ssrRenderAttrs as _ssrRenderAttrs, ssrInterpolate as _ssrInterpolate } from "vue/server-renderer"

import { ref } from 'vue'
        
export default {
  __ssrInlineRender: true,
  setup(__props) {

        const count = ref(0)
        const style = { color: 'red' }
        
return (_ctx, _push, _parent, _attrs) => {
  const _cssVars = { style: {
  "--xxxxxxxx-count": (count.value),
  "--xxxxxxxx-style\\\\.color": (style.color)
}}
  _push(\`<!--[--><div\${
    _ssrRenderAttrs(_cssVars)
  }>\${
    _ssrInterpolate(count.value)
  }</div><div\${
    _ssrRenderAttrs(_cssVars)
  }>static</div><!--]-->\`)
}
}

}"
`;

exports[`SFC compile <script setup> > inlineTemplate mode > template assignment expression codegen 1`] = `
"import { createElementVNode as _createElementVNode, isRef as _isRef, unref as _unref, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

import { ref } from 'vue'
        
export default {
  setup(__props) {

        const count = ref(0)
        const maybe = foo()
        let lett = 1
        let v = ref(1)
        
return (_ctx, _cache) => {
  return (_openBlock(), _createElementBlock(_Fragment, null, [
    _createElementVNode("div", {
      onClick: _cache[0] || (_cache[0] = $event => (count.value = 1))
    }),
    _createElementVNode("div", {
      onClick: _cache[1] || (_cache[1] = $event => (maybe.value = count.value))
    }),
    _createElementVNode("div", {
      onClick: _cache[2] || (_cache[2] = $event => (_isRef(lett) ? lett.value = count.value : lett = count.value))
    }),
    _createElementVNode("div", {
      onClick: _cache[3] || (_cache[3] = $event => (_isRef(v) ? v.value += 1 : v += 1))
    }),
    _createElementVNode("div", {
      onClick: _cache[4] || (_cache[4] = $event => (_isRef(v) ? v.value -= 1 : v -= 1))
    }),
    _createElementVNode("div", {
      onClick: _cache[5] || (_cache[5] = () => {
              let a = '' + _unref(lett)
              _isRef(v) ? v.value = a : v = a
           })
    }),
    _createElementVNode("div", {
      onClick: _cache[6] || (_cache[6] = () => {
              // nested scopes
              (()=>{
                let x = _ctx.a
                (()=>{
                  let z = x
                  let z2 = z
                })
                let lz = _ctx.z
              })
              _isRef(v) ? v.value = _ctx.a : v = _ctx.a
           })
    })
  ], 64 /* STABLE_FRAGMENT */))
}
}

}"
`;

exports[`SFC compile <script setup> > inlineTemplate mode > template destructure assignment codegen 1`] = `
"import { createElementVNode as _createElementVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

import { ref } from 'vue'
        
export default {
  setup(__props) {

        const val = {}
        const count = ref(0)
        const maybe = foo()
        let lett = 1
        
return (_ctx, _cache) => {
  return (_openBlock(), _createElementBlock(_Fragment, null, [
    _createElementVNode("div", {
      onClick: _cache[0] || (_cache[0] = $event => (({ count: count.value } = val)))
    }),
    _createElementVNode("div", {
      onClick: _cache[1] || (_cache[1] = $event => ([maybe.value] = val))
    }),
    _createElementVNode("div", {
      onClick: _cache[2] || (_cache[2] = $event => (({ lett: lett } = val)))
    })
  ], 64 /* STABLE_FRAGMENT */))
}
}

}"
`;

exports[`SFC compile <script setup> > inlineTemplate mode > template update expression codegen 1`] = `
"import { createElementVNode as _createElementVNode, isRef as _isRef, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

import { ref } from 'vue'
        
export default {
  setup(__props) {

        const count = ref(0)
        const maybe = foo()
        let lett = 1
        
return (_ctx, _cache) => {
  return (_openBlock(), _createElementBlock(_Fragment, null, [
    _createElementVNode("div", {
      onClick: _cache[0] || (_cache[0] = $event => (count.value++))
    }),
    _createElementVNode("div", {
      onClick: _cache[1] || (_cache[1] = $event => (--count.value))
    }),
    _createElementVNode("div", {
      onClick: _cache[2] || (_cache[2] = $event => (maybe.value++))
    }),
    _createElementVNode("div", {
      onClick: _cache[3] || (_cache[3] = $event => (--maybe.value))
    }),
    _createElementVNode("div", {
      onClick: _cache[4] || (_cache[4] = $event => (_isRef(lett) ? lett.value++ : lett++))
    }),
    _createElementVNode("div", {
      onClick: _cache[5] || (_cache[5] = $event => (_isRef(lett) ? --lett.value : --lett))
    })
  ], 64 /* STABLE_FRAGMENT */))
}
}

}"
`;

exports[`SFC compile <script setup> > inlineTemplate mode > unref + new expression 1`] = `
"import { unref as _unref, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

import Foo from './foo'
        
export default {
  setup(__props) {

        
return (_ctx, _cache) => {
  return (_openBlock(), _createElementBlock(_Fragment, null, [
    _createElementVNode("div", null, _toDisplayString(new (_unref(Foo))()), 1 /* TEXT */),
    _createElementVNode("div", null, _toDisplayString(new (_unref(Foo)).Bar()), 1 /* TEXT */)
  ], 64 /* STABLE_FRAGMENT */))
}
}

}"
`;

exports[`SFC compile <script setup> > inlineTemplate mode > v-model codegen 1`] = `
"import { vModelText as _vModelText, createElementVNode as _createElementVNode, withDirectives as _withDirectives, unref as _unref, isRef as _isRef, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

import { ref } from 'vue'
        
export default {
  setup(__props) {

        const count = ref(0)
        const maybe = foo()
        let lett = 1
        
return (_ctx, _cache) => {
  return (_openBlock(), _createElementBlock(_Fragment, null, [
    _withDirectives(_createElementVNode("input", {
      "onUpdate:modelValue": _cache[0] || (_cache[0] = $event => ((count).value = $event))
    }, null, 512 /* NEED_PATCH */), [
      [_vModelText, count.value]
    ]),
    _withDirectives(_createElementVNode("input", {
      "onUpdate:modelValue": _cache[1] || (_cache[1] = $event => (_isRef(maybe) ? (maybe).value = $event : null))
    }, null, 512 /* NEED_PATCH */), [
      [_vModelText, _unref(maybe)]
    ]),
    _withDirectives(_createElementVNode("input", {
      "onUpdate:modelValue": _cache[2] || (_cache[2] = $event => (_isRef(lett) ? (lett).value = $event : lett = $event))
    }, null, 512 /* NEED_PATCH */), [
      [_vModelText, _unref(lett)]
    ])
  ], 64 /* STABLE_FRAGMENT */))
}
}

}"
`;

exports[`SFC compile <script setup> > inlineTemplate mode > with defineExpose() 1`] = `
"
export default {
  setup(__props, { expose: __expose }) {

        const count = ref(0)
        __expose({ count })
        
return () => {}
}

}"
`;

exports[`SFC compile <script setup> > should compile JS syntax 1`] = `
"const a = 1
      const b = 2
      
export default {
  setup(__props, { expose: __expose }) {
  __expose();

      
return { a, b }
}

}"
`;

exports[`SFC compile <script setup> > should expose top level declarations 1`] = `
"import { x } from './x'
      
      import { xx } from './x'
      let aa = 1
      const bb = 2
      function cc() {}
      class dd {}
      

export default {
  setup(__props, { expose: __expose }) {
  __expose();

      let a = 1
      const b = 2
      function c() {}
      class d {}
      
return { get aa() { return aa }, set aa(v) { aa = v }, bb, cc, dd, get a() { return a }, set a(v) { a = v }, b, c, d, get xx() { return xx }, get x() { return x } }
}

}"
`;

exports[`SFC compile <script setup> > with TypeScript > const Enum 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
const enum Foo { A = 123 }
        
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

        
return { Foo }
}

})"
`;

exports[`SFC compile <script setup> > with TypeScript > hoist type declarations 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
export interface Foo {}
        type Bar = {}
      
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

        
return {  }
}

})"
`;

exports[`SFC compile <script setup> > with TypeScript > import type 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import type { Foo } from './main.ts'
        import { type Bar, Baz } from './main.ts'
        
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

        
return { get Baz() { return Baz } }
}

})"
`;

exports[`SFC compile <script setup> > with TypeScript > runtime Enum 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
enum Foo { A = 123 }
        
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

        
return { Foo }
}

})"
`;

exports[`SFC compile <script setup> > with TypeScript > runtime Enum in normal script 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

          export enum D { D = "D" }
          const enum C { C = "C" }
          enum B { B = "B" }
        
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

        enum Foo { A = 123 }
        
return { D, C, B, Foo }
}

})"
`;

exports[`SFC compile <script setup> > with TypeScript > with generic attribute 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
type Bar = {}
      
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

        
return {  }
}

})"
`;

exports[`SFC genDefaultAs > <script setup> only 1`] = `
"const a = 1
      
const _sfc_ = {
  setup(__props, { expose: __expose }) {
  __expose();

      
return { a }
}

}"
`;

exports[`SFC genDefaultAs > <script setup> only w/ ts 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
const a = 1
      
const _sfc_ = /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

      
return { a }
}

})"
`;

exports[`SFC genDefaultAs > <script> + <script setup> 1`] = `
"
      const __default__ = {}
      
const _sfc_ = /*#__PURE__*/Object.assign(__default__, {
  setup(__props, { expose: __expose }) {
  __expose();

      const a = 1
      
return { a }
}

})"
`;

exports[`SFC genDefaultAs > <script> + <script setup> 2`] = `
"
      const __default__ = {}
      
const _sfc_ = /*#__PURE__*/Object.assign(__default__, {
  setup(__props, { expose: __expose }) {
  __expose();

      const a = 1
      
return { a }
}

})"
`;

exports[`SFC genDefaultAs > <script> + <script setup> w/ ts 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

      const __default__ = {}
      
const _sfc_ = /*#__PURE__*/_defineComponent({
  ...__default__,
  setup(__props, { expose: __expose }) {
  __expose();

      const a = 1
      
return { a }
}

})"
`;

exports[`SFC genDefaultAs > normal <script> only 1`] = `
"
      const _sfc_ = {}
      "
`;

exports[`SFC genDefaultAs > normal <script> w/ cssVars 1`] = `
"
      const _sfc_ = {}
      
import { useCssVars as _useCssVars } from 'vue'
const __injectCSSVars__ = () => {
_useCssVars(_ctx => ({
  "xxxxxxxx-x": (_ctx.x)
}))}
const __setup__ = _sfc_.setup
_sfc_.setup = __setup__
  ? (props, ctx) => { __injectCSSVars__();return __setup__(props, ctx) }
  : __injectCSSVars__
"
`;

exports[`SFC genDefaultAs > parser plugins > import attributes (user override for deprecated syntax) 1`] = `
"import { foo } from './foo.js' assert { type: 'foobar' }
        
export default {
  setup(__props, { expose: __expose }) {
  __expose();

        
return { get foo() { return foo } }
}

}"
`;

exports[`SFC genDefaultAs > parser plugins > import attributes 1`] = `
"import { foo } from './foo.js' with { type: 'foobar' }
        
export default {
  setup(__props, { expose: __expose }) {
  __expose();

        
return { get foo() { return foo } }
}

}"
`;

exports[`compileScript > should care about runtimeModuleName 1`] = `
"import { withAsyncContext as _withAsyncContext } from "npm:vue"

export default {
  async setup(__props, { expose: __expose }) {
  __expose();

let __temp, __restore

        ;(
  ([__temp,__restore] = _withAsyncContext(() => Promise.resolve(1))),
  await __temp,
  __restore()
)
      
return {  }
}

}"
`;
