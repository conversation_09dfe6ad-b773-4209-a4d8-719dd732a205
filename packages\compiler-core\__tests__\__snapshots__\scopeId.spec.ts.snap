// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`scopeId compiler support > should push scopeId for hoisted nodes 1`] = `
"import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from "vue"

const _withScopeId = n => (_pushScopeId("test"),n=n(),_popScopeId(),n)
const _hoisted_1 = /*#__PURE__*/ _withScopeId(() => /*#__PURE__*/_createElementVNode("div", null, "hello", -1 /* HOISTED */))
const _hoisted_2 = /*#__PURE__*/ _withScopeId(() => /*#__PURE__*/_createElementVNode("div", null, "world", -1 /* HOISTED */))

export function render(_ctx, _cache) {
  return (_openBlock(), _createElementBlock("div", null, [
    _hoisted_1,
    _createTextVNode(_toDisplayString(_ctx.foo), 1 /* TEXT */),
    _hoisted_2
  ]))
}"
`;

exports[`scopeId compiler support > should wrap default slot 1`] = `
"import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from "vue"

export function render(_ctx, _cache) {
  const _component_Child = _resolveComponent("Child")

  return (_openBlock(), _createBlock(_component_Child, null, {
    default: _withCtx(() => [
      _createElementVNode("div")
    ]),
    _: 1 /* STABLE */
  }))
}"
`;

exports[`scopeId compiler support > should wrap dynamic slots 1`] = `
"import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, renderList as _renderList, createSlots as _createSlots, openBlock as _openBlock, createBlock as _createBlock } from "vue"

export function render(_ctx, _cache) {
  const _component_Child = _resolveComponent("Child")

  return (_openBlock(), _createBlock(_component_Child, null, _createSlots({ _: 2 /* DYNAMIC */ }, [
    (_ctx.ok)
      ? {
          name: "foo",
          fn: _withCtx(() => [
            _createElementVNode("div")
          ]),
          key: "0"
        }
      : undefined,
    _renderList(_ctx.list, (i) => {
      return {
        name: i,
        fn: _withCtx(() => [
          _createElementVNode("div")
        ])
      }
    })
  ]), 1024 /* DYNAMIC_SLOTS */))
}"
`;

exports[`scopeId compiler support > should wrap named slots 1`] = `
"import { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from "vue"

export function render(_ctx, _cache) {
  const _component_Child = _resolveComponent("Child")

  return (_openBlock(), _createBlock(_component_Child, null, {
    foo: _withCtx(({ msg }) => [
      _createTextVNode(_toDisplayString(msg), 1 /* TEXT */)
    ]),
    bar: _withCtx(() => [
      _createElementVNode("div")
    ]),
    _: 1 /* STABLE */
  }))
}"
`;
