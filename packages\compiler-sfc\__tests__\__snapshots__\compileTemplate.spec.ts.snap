// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`dynamic v-on + static v-on should merged 1`] = `
"import { toHandler<PERSON>ey as _toHandler<PERSON>ey, mergeProps as _mergeProps, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock } from "vue"

export function render(_ctx, _cache) {
  return (_openBlock(), _createElementBlock("input", _mergeProps({
    onBlur: _cache[0] || (_cache[0] = (...args) => (_ctx.onBlur && _ctx.onBlur(...args)))
  }, {
    [_toHandlerKey(_ctx.validateEvent)]: _cache[1] || (_cache[1] = (...args) => (_ctx.onValidateEvent && _ctx.onValidateEvent(...args)))
  }), null, 16 /* FULL_PROPS */))
}"
`;

exports[`should not hoist srcset URLs in SSR mode 1`] = `
"import { resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode } from "vue"
import { ssrRenderAttr as _ssrRenderAttr, ssrRenderComponent as _ssrRenderComponent } from "vue/server-renderer"
import _imports_0 from './img/foo.svg'
import _imports_1 from './img/bar.svg'


export function ssrRender(_ctx, _push, _parent, _attrs) {
  const _component_router_link = _resolveComponent("router-link")

  _push(\`<!--[--><picture><source\${
    _ssrRenderAttr("srcset", _imports_0)
  }><img\${
    _ssrRenderAttr("src", _imports_0)
  }></picture>\`)
  _push(_ssrRenderComponent(_component_router_link, null, {
    default: _withCtx((_, _push, _parent, _scopeId) => {
      if (_push) {
        _push(\`<picture\${
          _scopeId
        }><source\${
          _ssrRenderAttr("srcset", _imports_1)
        }\${
          _scopeId
        }><img\${
          _ssrRenderAttr("src", _imports_1)
        }\${
          _scopeId
        }></picture>\`)
      } else {
        return [
          _createVNode("picture", null, [
            _createVNode("source", {
              srcset: _imports_1
            }),
            _createVNode("img", { src: _imports_1 })
          ])
        ]
      }
    }),
    _: 1 /* STABLE */
  }, _parent))
  _push(\`<!--]-->\`)
}"
`;

exports[`template errors 1`] = `
[
  [SyntaxError: Error parsing JavaScript expression: Unexpected token (1:3)],
  [SyntaxError: v-model can only be used on <input>, <textarea> and <select> elements.],
]
`;
