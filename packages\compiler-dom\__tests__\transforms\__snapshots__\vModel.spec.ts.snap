// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: transform v-model > errors > should allow usage on custom element 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelText: _vModelText, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("my-input", {
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [_vModelText, model]
    ])
  }
}"
`;

exports[`compiler: transform v-model > input w/ dynamic v-bind 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelDynamic: _vModelDynamic, mergeProps: _mergeProps, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("input", _mergeProps(obj, {
      "onUpdate:modelValue": $event => ((model) = $event)
    }), null, 16 /* FULL_PROPS */, ["onUpdate:modelValue"])), [
      [_vModelDynamic, model]
    ])
  }
}"
`;

exports[`compiler: transform v-model > input w/ dynamic v-bind 2`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelDynamic: _vModelDynamic, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("input", {
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [_vModelDynamic, model]
    ])
  }
}"
`;

exports[`compiler: transform v-model > modifiers > .lazy 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelText: _vModelText, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("input", {
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [
        _vModelText,
        model,
        void 0,
        { lazy: true }
      ]
    ])
  }
}"
`;

exports[`compiler: transform v-model > modifiers > .number 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelText: _vModelText, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("input", {
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [
        _vModelText,
        model,
        void 0,
        { number: true }
      ]
    ])
  }
}"
`;

exports[`compiler: transform v-model > modifiers > .trim 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelText: _vModelText, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("input", {
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [
        _vModelText,
        model,
        void 0,
        { trim: true }
      ]
    ])
  }
}"
`;

exports[`compiler: transform v-model > simple expression 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelText: _vModelText, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("input", {
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [_vModelText, model]
    ])
  }
}"
`;

exports[`compiler: transform v-model > simple expression for input (checkbox) 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelCheckbox: _vModelCheckbox, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("input", {
      type: "checkbox",
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [_vModelCheckbox, model]
    ])
  }
}"
`;

exports[`compiler: transform v-model > simple expression for input (dynamic type) 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelDynamic: _vModelDynamic, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("input", {
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [_vModelDynamic, model]
    ])
  }
}"
`;

exports[`compiler: transform v-model > simple expression for input (radio) 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelRadio: _vModelRadio, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("input", {
      type: "radio",
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [_vModelRadio, model]
    ])
  }
}"
`;

exports[`compiler: transform v-model > simple expression for input (text) 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelText: _vModelText, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("input", {
      type: "text",
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [_vModelText, model]
    ])
  }
}"
`;

exports[`compiler: transform v-model > simple expression for select 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelSelect: _vModelSelect, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("select", {
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [_vModelSelect, model]
    ])
  }
}"
`;

exports[`compiler: transform v-model > simple expression for textarea 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { vModelText: _vModelText, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return _withDirectives((_openBlock(), _createElementBlock("textarea", {
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["onUpdate:modelValue"])), [
      [_vModelText, model]
    ])
  }
}"
`;
