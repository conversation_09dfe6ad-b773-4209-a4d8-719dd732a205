// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compile > should contain standard transforms 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { toDisplayString: _toDisplayString, createElementVNode: _createElementVNode, normalizeStyle: _normalizeStyle, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock(_Fragment, null, [
      _createElementVNode("div", {
        textContent: _toDisplayString(text)
      }, null, 8 /* PROPS */, ["textContent"]),
      _createElementVNode("div", { innerHTML: html }, null, 8 /* PROPS */, ["innerHTML"]),
      _createElementVNode("div", null, "test"),
      _createElementVNode("div", { style: {"color":"red"} }, "red"),
      _createElementVNode("div", {
        style: _normalizeStyle({color: 'green'})
      }, null, 4 /* STYLE */)
    ], 64 /* STABLE_FRAGMENT */))
  }
}"
`;
