// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: transform v-model > compound expression (with prefixIdentifiers) 1`] = `
"import { openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

export function render(_ctx, _cache) {
  return (_openBlock(), _createElementBlock("input", {
    modelValue: _ctx.model[_ctx.index],
    "onUpdate:modelValue": $event => ((_ctx.model[_ctx.index]) = $event)
  }, null, 8 /* PROPS */, ["modelValue", "onUpdate:modelValue"]))
}"
`;

exports[`compiler: transform v-model > compound expression 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("input", {
      modelValue: model[index],
      "onUpdate:modelValue": $event => ((model[index]) = $event)
    }, null, 8 /* PROPS */, ["modelValue", "onUpdate:modelValue"]))
  }
}"
`;

exports[`compiler: transform v-model > simple expression (with multilines) 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("input", {
      modelValue: 
 model
.
foo 
,
      "onUpdate:modelValue": $event => ((
 model
.
foo 
) = $event)
    }, null, 8 /* PROPS */, ["modelValue", "onUpdate:modelValue"]))
  }
}"
`;

exports[`compiler: transform v-model > simple expression (with prefixIdentifiers) 1`] = `
"import { openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

export function render(_ctx, _cache) {
  return (_openBlock(), _createElementBlock("input", {
    modelValue: _ctx.model,
    "onUpdate:modelValue": $event => ((_ctx.model) = $event)
  }, null, 8 /* PROPS */, ["modelValue", "onUpdate:modelValue"]))
}"
`;

exports[`compiler: transform v-model > simple expression 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("input", {
      modelValue: model,
      "onUpdate:modelValue": $event => ((model) = $event)
    }, null, 8 /* PROPS */, ["modelValue", "onUpdate:modelValue"]))
  }
}"
`;

exports[`compiler: transform v-model > with argument 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("input", {
      "foo-value": model,
      "onUpdate:fooValue": $event => ((model) = $event)
    }, null, 40 /* PROPS, NEED_HYDRATION */, ["foo-value", "onUpdate:fooValue"]))
  }
}"
`;

exports[`compiler: transform v-model > with dynamic argument (with prefixIdentifiers) 1`] = `
"import { normalizeProps as _normalizeProps, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

export function render(_ctx, _cache) {
  return (_openBlock(), _createElementBlock("input", _normalizeProps({
    [_ctx.value]: _ctx.model,
    ["onUpdate:" + _ctx.value]: $event => ((_ctx.model) = $event)
  }), null, 16 /* FULL_PROPS */))
}"
`;

exports[`compiler: transform v-model > with dynamic argument 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { normalizeProps: _normalizeProps, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("input", _normalizeProps({
      [value]: model,
      ["onUpdate:" + value]: $event => ((model) = $event)
    }), null, 16 /* FULL_PROPS */))
  }
}"
`;
