{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "arrow", "version": 799, "versionNonce": 529220601, "isDeleted": false, "id": "Gao2krnDddLMCj468JSWD", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 860.0129225738813, "y": 663.9911710635109, "strokeColor": "#f08c00", "backgroundColor": "#ffc9c9", "width": 133.75296854079784, "height": 149.58016791936518, "seed": 1415631543, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false, "startBinding": null, "endBinding": {"elementId": "hDC6an14QljktaZCUhcPF", "focus": 0.09950793234484598, "gap": 1.2432497743127229}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [25.209039386719837, 85.96948921803892], [133.75296854079784, 149.58016791936518]]}, {"type": "arrow", "version": 563, "versionNonce": 290881303, "isDeleted": false, "id": "N3wyyEU7TQ8BsOQgxCmlR", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 292.88008929085873, "y": 660.7027503334302, "strokeColor": "#2f9e44", "backgroundColor": "#b2f2bb", "width": 936.9972134376155, "height": 1.3184243543457796, "seed": 534235417, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [936.9972134376155, -1.3184243543457796]]}, {"type": "arrow", "version": 302, "versionNonce": 883286489, "isDeleted": false, "id": "nRDWQs5nQa37yzCWTBiXC", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 293.1231624544633, "y": 820.6017661012943, "strokeColor": "#f08c00", "backgroundColor": "#b2f2bb", "width": 790.7091601354882, "height": 0.35284814071621895, "seed": 515907671, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false, "startBinding": null, "endBinding": {"elementId": "ggogfJT7E_bbfEog7Hjnp", "focus": -0.14000162237652433, "gap": 1}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [790.7091601354882, -0.35284814071621895]]}, {"type": "text", "version": 36, "versionNonce": 981763127, "isDeleted": false, "id": "ZPdMAnEUq5Jgj1W07Zqiw", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 292.0450153578305, "y": 619.3959946602608, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 46.875, "height": 24, "seed": 1311694519, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "main", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "main", "lineHeight": 1.2, "baseline": 20}, {"type": "text", "version": 94, "versionNonce": 18759353, "isDeleted": false, "id": "g9IkEIfu4vA8Qkwtw01Hi", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 290.88990199912035, "y": 779.1760596323645, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 58.59375, "height": 24, "seed": 329886135, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "minor", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "minor", "lineHeight": 1.2, "baseline": 20}, {"type": "ellipse", "version": 50, "versionNonce": 1442112855, "isDeleted": false, "id": "RrdEQ7hwgGGDPhzDnuZj1", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 361.55609907891005, "y": 649.8742329483416, "strokeColor": "#2f9e44", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 2077639991, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 79, "versionNonce": 1547173785, "isDeleted": false, "id": "Zmp49FKWxGSzKnVKomjQc", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 427.3015090315691, "y": 650.256485100784, "strokeColor": "#2f9e44", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 372652121, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 76, "versionNonce": 586949239, "isDeleted": false, "id": "UOl9nLBksM7RPdH9mzjJa", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 490.9435520120701, "y": 651.2601420343765, "strokeColor": "#2f9e44", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 508667545, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 120, "versionNonce": 874947705, "isDeleted": false, "id": "oMC55V0VO_hOXoZ1se8Kl", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 555.4481126698772, "y": 650.7975189165487, "strokeColor": "#2f9e44", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 1914963513, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 66, "versionNonce": 39762839, "isDeleted": false, "id": "DZY5DC5uVP7-U5c3ngIZ4", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 622.5167031502219, "y": 649.3743647489936, "strokeColor": "#2f9e44", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 165914713, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 107, "versionNonce": 1689103705, "isDeleted": false, "id": "Vsw6oIiTM3fQypkiCic3f", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 690.330195260967, "y": 650.6681412649529, "strokeColor": "#2f9e44", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 280044345, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"id": "lwYvAs-7FTjcwxKjcx0KV", "type": "arrow"}], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 148, "versionNonce": 1986194201, "isDeleted": false, "id": "D14w9erv_2l53mINe2nSt", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 361.004283792179, "y": 810.2809579853473, "strokeColor": "#f08c00", "backgroundColor": "#ffc9c9", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 1203257975, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 179, "versionNonce": 1172811511, "isDeleted": false, "id": "6WO8xOpG0rf673b_bT0m7", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 426.74969374483805, "y": 810.6632101377896, "strokeColor": "#f08c00", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 2056706967, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"id": "mE8Mu0qKfFaWPCC5vmF_f", "type": "arrow"}], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 173, "versionNonce": 820518905, "isDeleted": false, "id": "VB9U8oH-78hf530hIb_mG", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 490.391736725339, "y": 811.6668670713822, "strokeColor": "#f08c00", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 1149587639, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 218, "versionNonce": 1227143191, "isDeleted": false, "id": "Bxv1hcS0VmxUwI0JLFH97", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 554.8962973831461, "y": 811.2042439535543, "strokeColor": "#f08c00", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 1864901079, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"id": "M14Q0Uo1DBy2Ss2SOFSgW", "type": "arrow"}], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 167, "versionNonce": 1387509977, "isDeleted": false, "id": "4v23gkfhy-hzk18YdkfLz", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 621.9648878634908, "y": 809.7810897859994, "strokeColor": "#f08c00", "backgroundColor": "#ffc9c9", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 462671607, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"id": "vEF1cIIYYWKm84KLKqEz3", "type": "arrow"}], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 200, "versionNonce": 774085943, "isDeleted": false, "id": "AtEf7o4WZQn4Zxq8EN5fH", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 689.7783799742359, "y": 811.0748663019584, "strokeColor": "#f08c00", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 1414322199, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"id": "3heKY3vfe3-6ni4dX7Uqo", "type": "arrow"}], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 199, "versionNonce": 1834563001, "isDeleted": false, "id": "ugDby5sBv4NKdNt8eC1sg", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 762.6179978227377, "y": 810.2986003923828, "strokeColor": "#f08c00", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 1598537015, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false}, {"type": "ellipse", "version": 211, "versionNonce": 407428695, "isDeleted": false, "id": "Fwe4F2sB_0jptOZGYsusj", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 837.1081608628116, "y": 810.859236882632, "strokeColor": "#f08c00", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 1340669527, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"id": "M14Q0Uo1DBy2Ss2SOFSgW", "type": "arrow"}], "updated": 1698927613071, "link": null, "locked": false}, {"type": "arrow", "version": 57, "versionNonce": 335287961, "isDeleted": false, "id": "mE8Mu0qKfFaWPCC5vmF_f", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 437.60867586595543, "y": 830.4227236701945, "strokeColor": "#f08c00", "backgroundColor": "#ffc9c9", "width": 0.5232394659406623, "height": 33.25787987764363, "seed": 482155929, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false, "startBinding": {"elementId": "6WO8xOpG0rf673b_bT0m7", "focus": -0.1727591064041787, "gap": 1.046152088903881}, "endBinding": {"elementId": "JALHBtowuh3_a86loej2x", "focus": 0.015156451076917701, "gap": 15.586906139714472}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-0.5232394659406623, 33.25787987764363]]}, {"type": "arrow", "version": 59, "versionNonce": 1248394103, "isDeleted": false, "id": "AI-_jSAuzesxTqwRvpk0s", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 501.2878833373983, "y": 652.3088851192829, "strokeColor": "#2f9e44", "backgroundColor": "#ffc9c9", "width": 0, "height": 40.40111211199792, "seed": 1052632343, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [0, -40.40111211199792]]}, {"type": "arrow", "version": 261, "versionNonce": 693099385, "isDeleted": false, "id": "lwYvAs-7FTjcwxKjcx0KV", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 786.7392304423553, "y": 649.6016935672433, "strokeColor": "#2f9e44", "backgroundColor": "#ffc9c9", "width": 0, "height": 40.40111211199792, "seed": 1233043511, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false, "startBinding": {"elementId": "s0PKxsWTJSDbQeEl_WI-C", "focus": 0.016372633695398757, "gap": 1}, "endBinding": {"elementId": "9ia1Uwc5X0fRw5iaahmcT", "focus": 0.025318405829282714, "gap": 14.862364635333904}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [0, -40.40111211199792]]}, {"type": "text", "version": 121, "versionNonce": 952661143, "isDeleted": false, "id": "qWW8uxDIcV3Bkj28uvRLr", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 454.32425448306674, "y": 537.8854189061962, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 93.75, "height": 57.599999999999994, "seed": 809847769, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "patch\nrelease\ne.g. 3.3.8", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "patch\nrelease\ne.g. 3.3.8", "lineHeight": 1.2, "baseline": 53}, {"type": "text", "version": 257, "versionNonce": 1838679129, "isDeleted": false, "id": "9ia1Uwc5X0fRw5iaahmcT", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 741.0510307156029, "y": 536.7382168199114, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 93.75, "height": 57.599999999999994, "seed": 213765431, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "lwYvAs-7FTjcwxKjcx0KV", "type": "arrow"}], "updated": 1698927613071, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "patch\nrelease\ne.g. 3.3.9", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "patch\nrelease\ne.g. 3.3.9", "lineHeight": 1.2, "baseline": 53}, {"type": "text", "version": 222, "versionNonce": 1528547767, "isDeleted": false, "id": "JALHBtowuh3_a86loej2x", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 350.7264132088442, "y": 879.2675096875524, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 168.75, "height": 57.599999999999994, "seed": 41180921, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "mE8Mu0qKfFaWPCC5vmF_f", "type": "arrow"}], "updated": 1698927613071, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "pre minor\nrelease\ne.g. 3.4.0-alpha.1", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "pre minor\nrelease\ne.g. 3.4.0-alpha.1", "lineHeight": 1.2, "baseline": 53}, {"type": "arrow", "version": 345, "versionNonce": 1286082873, "isDeleted": false, "id": "3heKY3vfe3-6ni4dX7Uqo", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 699.5281288163526, "y": 831.0290882554708, "strokeColor": "#f08c00", "backgroundColor": "#ffc9c9", "width": 0.5502191262773977, "height": 33.25154356841597, "seed": *********, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613071, "link": null, "locked": false, "startBinding": {"elementId": "AtEf7o4WZQn4Zxq8EN5fH", "focus": -0.05612657009295625, "gap": 1.1451322685712295}, "endBinding": {"elementId": "9t6qH-tAxVUexkHHi2pd2", "focus": 0.015156451076917755, "gap": 15.586906139714358}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-0.5502191262773977, 33.25154356841597]]}, {"type": "text", "version": 365, "versionNonce": 1049066199, "isDeleted": false, "id": "9t6qH-tAxVUexkHHi2pd2", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 617.3409291322284, "y": 879.8675379636011, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 159.375, "height": 57.599999999999994, "seed": 1013545943, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "3heKY3vfe3-6ni4dX7Uqo", "type": "arrow"}], "updated": 1698927613071, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "pre minor\nrelease\ne.g. 3.4.0-beta.1", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "pre minor\nrelease\ne.g. 3.4.0-beta.1", "lineHeight": 1.2, "baseline": 53}, {"type": "arrow", "version": 788, "versionNonce": 1810072089, "isDeleted": false, "id": "vEF1cIIYYWKm84KLKqEz3", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 630.3597332113623, "y": 667.2735668205443, "strokeColor": "#f08c00", "backgroundColor": "#ffc9c9", "width": 2.258228100583324, "height": 140.75112333166828, "seed": 2091697367, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false, "startBinding": null, "endBinding": {"elementId": "4v23gkfhy-hzk18YdkfLz", "focus": 0.13930391883256707, "gap": 1.8256906627890626}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [1.8426514015177418, 69.09942755691065], [2.258228100583324, 140.75112333166828]]}, {"type": "arrow", "version": 687, "versionNonce": 2017318649, "isDeleted": false, "id": "M14Q0Uo1DBy2Ss2SOFSgW", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 370.5976915356099, "y": 667.5155013947814, "strokeColor": "#f08c00", "backgroundColor": "#ffc9c9", "width": 1.5329291446666957, "height": 145.39303664953377, "seed": 361678233, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-0.34892760581925586, 83.56228079137543], [1.1840015388474399, 145.39303664953377]]}, {"type": "text", "version": 537, "versionNonce": 342487319, "isDeleted": false, "id": "CHAOOJMz7tNaG1VsG_uzT", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 384.81046417498214, "y": 725.4677076298137, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 131.25, "height": 57.599999999999994, "seed": 1656007289, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "merge main\ninto minor\nbefore release", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "merge main\ninto minor\nbefore release", "lineHeight": 1.2, "baseline": 53}, {"type": "ellipse", "version": 202, "versionNonce": 876253145, "isDeleted": false, "id": "hDC6an14QljktaZCUhcPF", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 993.0386151813434, "y": 810.335845473903, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 1433430105, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"id": "Gao2krnDddLMCj468JSWD", "type": "arrow"}], "updated": 1698927613072, "link": null, "locked": false}, {"type": "arrow", "version": 1525, "versionNonce": 777631287, "isDeleted": false, "id": "ces8IwHCpQlTnELpjFDIn", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 1092.5386800881793, "y": 827.5114796878765, "strokeColor": "#f08c00", "backgroundColor": "#ffc9c9", "width": 0.3315362017829102, "height": 49.45191086419197, "seed": 225867737, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false, "startBinding": null, "endBinding": {"elementId": "8rWUxp-jRNGrGRmhHHfm4", "focus": -0.2047594653982401, "gap": 10.392197401393389}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-0.3315362017829102, 49.45191086419197]]}, {"type": "text", "version": 894, "versionNonce": 1173171385, "isDeleted": false, "id": "8rWUxp-jRNGrGRmhHHfm4", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 1047.251646167428, "y": 887.3555879534618, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 112.5, "height": 57.599999999999994, "seed": 1600918713, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "ces8IwHCpQlTnELpjFDIn", "type": "arrow"}], "updated": 1698927613072, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "stable minor\nrelease\ne.g. 3.4.0", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "stable minor\nrelease\ne.g. 3.4.0", "lineHeight": 1.2, "baseline": 53}, {"type": "ellipse", "version": 201, "versionNonce": 78435447, "isDeleted": false, "id": "3RHuRn_evSK0YUe02B4MY", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 909.9742423218671, "y": 810.4142561718397, "strokeColor": "#2f9e44", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 1199705047, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false}, {"type": "ellipse", "version": 371, "versionNonce": 2093872087, "isDeleted": false, "id": "9h2Cu__8owLUgUGjGcWDe", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 848.4414471158692, "y": 650.826922928275, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 603147257, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false}, {"type": "ellipse", "version": 361, "versionNonce": 1981618457, "isDeleted": false, "id": "s0PKxsWTJSDbQeEl_WI-C", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 777.1778842958995, "y": 650.2466837635417, "strokeColor": "#2f9e44", "backgroundColor": "#b2f2bb", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 326722777, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"id": "lwYvAs-7FTjcwxKjcx0KV", "type": "arrow"}], "updated": 1698927613072, "link": null, "locked": false}, {"type": "text", "version": 871, "versionNonce": 1528156247, "isDeleted": false, "id": "3JAdSa7kqqSDSom5ZFDoE", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 904.3603861670398, "y": 707.2413714353705, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 140.625, "height": 57.599999999999994, "seed": 1011049431, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "final merge\nmain into minor\nbefore release", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "final merge\nmain into minor\nbefore release", "lineHeight": 1.2, "baseline": 53}, {"type": "arrow", "version": 591, "versionNonce": 1714373785, "isDeleted": false, "id": "7kFBLq2Iczmj0lVnVk8Ad", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 1100.7141458557703, "y": 814.2034531496416, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "width": 127.38209933342364, "height": 144.5383600420214, "seed": 25829591, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false, "startBinding": null, "endBinding": {"elementId": "Y7VXnuc9QEz2N2l9i0xrc", "focus": 0.3932764551319699, "gap": 5.928572790502042}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [88.94909573964219, -43.721805169626464], [127.38209933342364, -144.5383600420214]]}, {"type": "text", "version": 1208, "versionNonce": 1254600055, "isDeleted": false, "id": "gwFWlPLabuYhxCOweJjWz", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 1223.0464288187204, "y": 725.1565933898091, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 150, "height": 38.4, "seed": 51102743, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "main merge minor\n(fast forward)", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "main merge minor\n(fast forward)", "lineHeight": 1.2, "baseline": 34}, {"type": "ellipse", "version": 597, "versionNonce": 1760381305, "isDeleted": false, "id": "Y7VXnuc9QEz2N2l9i0xrc", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 1227.4473966637659, "y": 647.6689320688656, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 412038615, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"id": "7kFBLq2Iczmj0lVnVk8Ad", "type": "arrow"}], "updated": 1698927613072, "link": null, "locked": false}, {"type": "ellipse", "version": 547, "versionNonce": 1585505943, "isDeleted": false, "id": "ggogfJT7E_bbfEog7Hjnp", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 1083.7911569735343, "y": 809.5203742153592, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "width": 18.814646969963974, "height": 18.814646969963974, "seed": 741463161, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"id": "nRDWQs5nQa37yzCWTBiXC", "type": "arrow"}], "updated": 1698927613072, "link": null, "locked": false}, {"type": "text", "version": 229, "versionNonce": 1935127129, "isDeleted": false, "id": "eU-EgpwDD42CLYUEIDLaD", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 305.8405004265049, "y": 389.31989430571576, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "width": 581.25, "height": 19.2, "seed": 1086231577, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "- merge feature PRs into, and release minors from minor branch", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "- merge feature PRs into, and release minors from minor branch", "lineHeight": 1.2, "baseline": 15}, {"type": "text", "version": 397, "versionNonce": 116088535, "isDeleted": false, "id": "Kt6VBAVD4sLM4IexsRGoX", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 305.4136207977353, "y": 358.61173442109686, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "width": 618.75, "height": 19.2, "seed": 273353945, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1698927617946, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "- merge fix / chore PRs into, and release patches from main branch", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "- merge fix / chore PRs into, and release patches from main branch", "lineHeight": 1.2, "baseline": 15}, {"type": "text", "version": 459, "versionNonce": 440532793, "isDeleted": false, "id": "JwKEdnU6H_Nu74WbEAX5M", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 305.6723761009271, "y": 418.3724478537203, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "width": 459.375, "height": 19.2, "seed": 1001222329, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "- merge main into minor before each minor release", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "- merge main into minor before each minor release", "lineHeight": 1.2, "baseline": 15}, {"type": "text", "version": 602, "versionNonce": 1108720119, "isDeleted": false, "id": "mb9ZoP803MiH7MTO8wH-2", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 305.0895924262568, "y": 447.44321411383333, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "width": 534.375, "height": 19.2, "seed": 264651479, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "- fast forward main to minor after a stable minor release", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "- fast forward main to minor after a stable minor release", "lineHeight": 1.2, "baseline": 15}, {"type": "text", "version": 612, "versionNonce": 1588872441, "isDeleted": false, "id": "IfJPOFiwrCibpaBQqc5g-", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 2, "opacity": 100, "angle": 0, "x": 646.7131179044119, "y": 724.4984335940012, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 131.25, "height": 57.599999999999994, "seed": 1301100087, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1698927613072, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "merge main\ninto minor\nbefore release", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "merge main\ninto minor\nbefore release", "lineHeight": 1.2, "baseline": 53}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}