name: size data

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
      - minor

permissions:
  contents: read

env:
  PUPPETEER_SKIP_DOWNLOAD: 'true'

jobs:
  upload:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v3.0.0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: pnpm

      - name: Install dependencies
        run: pnpm install

      - run: pnpm run size

      - name: Upload Size Data
        uses: actions/upload-artifact@v4
        with:
          name: size-data
          path: temp/size

      - name: Save PR number
        if: ${{github.event_name == 'pull_request'}}
        run: echo ${{ github.event.number }} > ./pr.txt

      - uses: actions/upload-artifact@v4
        if: ${{github.event_name == 'pull_request'}}
        with:
          name: pr-number
          path: pr.txt
