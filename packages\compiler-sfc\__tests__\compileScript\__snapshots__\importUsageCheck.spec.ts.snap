// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`TS annotations 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { Foo, Bar, Baz, Qux, Fred } from './x'
    const a = 1
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

    function b() {}
    
return { a, b, get Baz() { return Baz } }
}

})"
`;

exports[`attribute expressions 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { bar, baz } from './x'
    const cond = true
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

    
return { cond, get bar() { return bar }, get baz() { return baz } }
}

})"
`;

exports[`components 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { FooBar, FooBaz, Foo<PERSON>ux, foo } from './x'
    const fooBar: FooBar = 1
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

    
return { fooBar, get FooBaz() { return FooBaz }, get FooQux() { return FooQux }, get foo() { return foo } }
}

})"
`;

exports[`directive 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { vMyDir } from './x'
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

    
return { get vMyDir() { return vMyDir } }
}

})"
`;

exports[`dynamic arguments 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { FooBar, foo, bar, unused, baz } from './x'
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

    
return { get FooBar() { return FooBar }, get foo() { return foo }, get bar() { return bar }, get baz() { return baz } }
}

})"
`;

exports[`js template string interpolations 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { VAR, VAR2, VAR3 } from './x'
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

    
return { get VAR() { return VAR }, get VAR3() { return VAR3 } }
}

})"
`;

exports[`last tag 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { FooBaz, Last } from './x'
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

    
return { get FooBaz() { return FooBaz }, get Last() { return Last } }
}

})"
`;

exports[`namespace / dot component usage 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import * as Foo from './foo'
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

      
return { get Foo() { return Foo } }
}

})"
`;

exports[`property access (whitespace) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { Foo, Bar, Baz } from './foo'
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

      
return { get Foo() { return Foo } }
}

})"
`;

exports[`property access 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { Foo, Bar, Baz } from './foo'
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

      
return { get Foo() { return Foo } }
}

})"
`;

exports[`spread operator 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { Foo, Bar, Baz } from './foo'
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

      
return { get Foo() { return Foo } }
}

})"
`;

exports[`template ref 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { foo, bar, Baz } from './foo'
    
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

      
return { get foo() { return foo }, get bar() { return bar }, get Baz() { return Baz } }
}

})"
`;

exports[`vue interpolations 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
import { x, y, z, x$y } from './x'
  
export default /*#__PURE__*/_defineComponent({
  setup(__props, { expose: __expose }) {
  __expose();

  
return { get x() { return x }, get z() { return z }, get x$y() { return x$y } }
}

})"
`;
