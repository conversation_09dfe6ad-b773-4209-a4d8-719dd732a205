// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: v-for > codegen > basic v-for 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (item) => {
      return (_openBlock(), _createElementBlock("span"))
    }), 256 /* UNKEYED_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > keyed template v-for 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, createElementVNode: _createElementVNode } = _Vue

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (item) => {
      return (_openBlock(), _createElementBlock(_Fragment, { key: item }, [
        "hello",
        _createElementVNode("span")
      ], 64 /* STABLE_FRAGMENT */))
    }), 128 /* KEYED_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > keyed v-for 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (item) => {
      return (_openBlock(), _createElementBlock("span", { key: item }))
    }), 128 /* KEYED_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > skipped key 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (item, __, index) => {
      return (_openBlock(), _createElementBlock("span"))
    }), 256 /* UNKEYED_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > skipped value & key 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (_, __, index) => {
      return (_openBlock(), _createElementBlock("span"))
    }), 256 /* UNKEYED_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > skipped value 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (_, key, index) => {
      return (_openBlock(), _createElementBlock("span"))
    }), 256 /* UNKEYED_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > template v-for 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, createElementVNode: _createElementVNode } = _Vue

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (item) => {
      return (_openBlock(), _createElementBlock(_Fragment, null, [
        "hello",
        _createElementVNode("span")
      ], 64 /* STABLE_FRAGMENT */))
    }), 256 /* UNKEYED_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > template v-for key injection with single child 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (item) => {
      return (_openBlock(), _createElementBlock("span", {
        key: item.id,
        id: item.id
      }, null, 8 /* PROPS */, ["id"]))
    }), 128 /* KEYED_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > template v-for w/ <slot/> 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, renderSlot: _renderSlot } = _Vue

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (item) => {
      return _renderSlot($slots, "default")
    }), 256 /* UNKEYED_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > v-for on <slot/> 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, renderSlot: _renderSlot } = _Vue

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (item) => {
      return _renderSlot($slots, "default")
    }), 256 /* UNKEYED_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > v-for on element with custom directive 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, resolveDirective: _resolveDirective, withDirectives: _withDirectives } = _Vue

    const _directive_foo = _resolveDirective("foo")

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(list, (i) => {
      return _withDirectives((_openBlock(), _createElementBlock("div", null, null, 512 /* NEED_PATCH */)), [
        [_directive_foo]
      ])
    }), 256 /* UNKEYED_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > v-for with constant expression 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, toDisplayString: _toDisplayString, createElementVNode: _createElementVNode } = _Vue

    return (_openBlock(), _createElementBlock(_Fragment, null, _renderList(10, (item) => {
      return _createElementVNode("p", null, _toDisplayString(item), 1 /* TEXT */)
    }), 64 /* STABLE_FRAGMENT */))
  }
}"
`;

exports[`compiler: v-for > codegen > v-if + v-for 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode } = _Vue

    return ok
      ? (_openBlock(true), _createElementBlock(_Fragment, { key: 0 }, _renderList(list, (i) => {
          return (_openBlock(), _createElementBlock("div"))
        }), 256 /* UNKEYED_FRAGMENT */))
      : _createCommentVNode("v-if", true)
  }
}"
`;

exports[`compiler: v-for > codegen > v-if + v-for on <template> 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode } = _Vue

    return ok
      ? (_openBlock(true), _createElementBlock(_Fragment, { key: 0 }, _renderList(list, (i) => {
          return (_openBlock(), _createElementBlock(_Fragment, null, [], 64 /* STABLE_FRAGMENT */))
        }), 256 /* UNKEYED_FRAGMENT */))
      : _createCommentVNode("v-if", true)
  }
}"
`;

exports[`compiler: v-for > codegen > value + key + index 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (item, key, index) => {
      return (_openBlock(), _createElementBlock("span"))
    }), 256 /* UNKEYED_FRAGMENT */))
  }
}"
`;
