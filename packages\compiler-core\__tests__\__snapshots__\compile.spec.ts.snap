// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: integration tests > function mode 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { toDisplayString: _toDisplayString, openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode, createTextVNode: _createTextVNode, Fragment: _Fragment, renderList: _renderList, createElementVNode: _createElementVNode, normalizeClass: _normalizeClass } = _Vue

    return (_openBlock(), _createElementBlock("div", {
      id: "foo",
      class: _normalizeClass(bar.baz)
    }, [
      _createTextVNode(_toDisplayString(world.burn()) + " ", 1 /* TEXT */),
      ok
        ? (_openBlock(), _createElementBlock("div", { key: 0 }, "yes"))
        : (_openBlock(), _createElementBlock(_Fragment, { key: 1 }, [
            _createTextVNode("no")
          ], 64 /* STABLE_FRAGMENT */)),
      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(list, (value, index) => {
        return (_openBlock(), _createElementBlock("div", null, [
          _createElementVNode("span", null, _toDisplayString(value + index), 1 /* TEXT */)
        ]))
      }), 256 /* UNKEYED_FRAGMENT */))
    ], 2 /* CLASS */))
  }
}"
`;

exports[`compiler: integration tests > function mode w/ prefixIdentifiers: true 1`] = `
"const { toDisplayString: _toDisplayString, openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode, createTextVNode: _createTextVNode, Fragment: _Fragment, renderList: _renderList, createElementVNode: _createElementVNode, normalizeClass: _normalizeClass } = Vue

return function render(_ctx, _cache) {
  return (_openBlock(), _createElementBlock("div", {
    id: "foo",
    class: _normalizeClass(_ctx.bar.baz)
  }, [
    _createTextVNode(_toDisplayString(_ctx.world.burn()) + " ", 1 /* TEXT */),
    (_ctx.ok)
      ? (_openBlock(), _createElementBlock("div", { key: 0 }, "yes"))
      : (_openBlock(), _createElementBlock(_Fragment, { key: 1 }, [
          _createTextVNode("no")
        ], 64 /* STABLE_FRAGMENT */)),
    (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.list, (value, index) => {
      return (_openBlock(), _createElementBlock("div", null, [
        _createElementVNode("span", null, _toDisplayString(value + index), 1 /* TEXT */)
      ]))
    }), 256 /* UNKEYED_FRAGMENT */))
  ], 2 /* CLASS */))
}"
`;

exports[`compiler: integration tests > module mode 1`] = `
"import { toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, Fragment as _Fragment, renderList as _renderList, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass } from "vue"

export function render(_ctx, _cache) {
  return (_openBlock(), _createElementBlock("div", {
    id: "foo",
    class: _normalizeClass(_ctx.bar.baz)
  }, [
    _createTextVNode(_toDisplayString(_ctx.world.burn()) + " ", 1 /* TEXT */),
    (_ctx.ok)
      ? (_openBlock(), _createElementBlock("div", { key: 0 }, "yes"))
      : (_openBlock(), _createElementBlock(_Fragment, { key: 1 }, [
          _createTextVNode("no")
        ], 64 /* STABLE_FRAGMENT */)),
    (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.list, (value, index) => {
      return (_openBlock(), _createElementBlock("div", null, [
        _createElementVNode("span", null, _toDisplayString(value + index), 1 /* TEXT */)
      ]))
    }), 256 /* UNKEYED_FRAGMENT */))
  ], 2 /* CLASS */))
}"
`;
