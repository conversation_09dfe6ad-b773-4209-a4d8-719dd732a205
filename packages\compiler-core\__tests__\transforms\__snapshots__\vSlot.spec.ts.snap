// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: transform component slots > dynamically named slots 1`] = `
"const { toDisplayString: _toDisplayString, resolveComponent: _resolveComponent, withCtx: _withCtx, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, {
    [_ctx.one]: _withCtx(({ foo }) => [_toDisplayString(foo), _toDisplayString(_ctx.bar)]),
    [_ctx.two]: _withCtx(({ bar }) => [_toDisplayString(_ctx.foo), _toDisplayString(bar)]),
    _: 2 /* DYNAMIC */
  }, 1024 /* DYNAMIC_SLOTS */))
}"
`;

exports[`compiler: transform component slots > implicit default slot 1`] = `
"const { createElementVNode: _createElementVNode, resolveComponent: _resolveComponent, withCtx: _withCtx, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, {
    default: _withCtx(() => [
      _createElementVNode("div")
    ]),
    _: 1 /* STABLE */
  }))
}"
`;

exports[`compiler: transform component slots > named slot with v-for w/ prefixIdentifiers: true 1`] = `
"const { toDisplayString: _toDisplayString, resolveComponent: _resolveComponent, withCtx: _withCtx, renderList: _renderList, createSlots: _createSlots, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, _createSlots({ _: 2 /* DYNAMIC */ }, [
    _renderList(_ctx.list, (name) => {
      return {
        name: name,
        fn: _withCtx(() => [_toDisplayString(name)])
      }
    })
  ]), 1024 /* DYNAMIC_SLOTS */))
}"
`;

exports[`compiler: transform component slots > named slot with v-if + prefixIdentifiers: true 1`] = `
"const { toDisplayString: _toDisplayString, resolveComponent: _resolveComponent, withCtx: _withCtx, createSlots: _createSlots, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, _createSlots({ _: 2 /* DYNAMIC */ }, [
    (_ctx.ok)
      ? {
          name: "one",
          fn: _withCtx((props) => [_toDisplayString(props)]),
          key: "0"
        }
      : undefined
  ]), 1024 /* DYNAMIC_SLOTS */))
}"
`;

exports[`compiler: transform component slots > named slot with v-if + v-else-if + v-else 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { resolveComponent: _resolveComponent, withCtx: _withCtx, createSlots: _createSlots, openBlock: _openBlock, createBlock: _createBlock } = _Vue

    const _component_Comp = _resolveComponent("Comp")

    return (_openBlock(), _createBlock(_component_Comp, null, _createSlots({ _: 2 /* DYNAMIC */ }, [
      ok
        ? {
            name: "one",
            fn: _withCtx(() => ["foo"]),
            key: "0"
          }
        : orNot
          ? {
              name: "two",
              fn: _withCtx((props) => ["bar"]),
              key: "1"
            }
          : {
              name: "one",
              fn: _withCtx(() => ["baz"]),
              key: "2"
            }
    ]), 1024 /* DYNAMIC_SLOTS */))
  }
}"
`;

exports[`compiler: transform component slots > named slot with v-if 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { resolveComponent: _resolveComponent, withCtx: _withCtx, createSlots: _createSlots, openBlock: _openBlock, createBlock: _createBlock } = _Vue

    const _component_Comp = _resolveComponent("Comp")

    return (_openBlock(), _createBlock(_component_Comp, null, _createSlots({ _: 2 /* DYNAMIC */ }, [
      ok
        ? {
            name: "one",
            fn: _withCtx(() => ["hello"]),
            key: "0"
          }
        : undefined
    ]), 1024 /* DYNAMIC_SLOTS */))
  }
}"
`;

exports[`compiler: transform component slots > named slots w/ implicit default slot 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createElementVNode: _createElementVNode, resolveComponent: _resolveComponent, withCtx: _withCtx, openBlock: _openBlock, createBlock: _createBlock } = _Vue

    const _component_Comp = _resolveComponent("Comp")

    return (_openBlock(), _createBlock(_component_Comp, null, {
      one: _withCtx(() => ["foo"]),
      default: _withCtx(() => [
        "bar",
        _createElementVNode("span")
      ]),
      _: 1 /* STABLE */
    }))
  }
}"
`;

exports[`compiler: transform component slots > nested slots scoping 1`] = `
"const { toDisplayString: _toDisplayString, resolveComponent: _resolveComponent, withCtx: _withCtx, createVNode: _createVNode, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Inner = _resolveComponent("Inner")
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, {
    default: _withCtx(({ foo }) => [
      _createVNode(_component_Inner, null, {
        default: _withCtx(({ bar }) => [_toDisplayString(foo), _toDisplayString(bar), _toDisplayString(_ctx.baz)]),
        _: 2 /* DYNAMIC */
      }, 1024 /* DYNAMIC_SLOTS */),
      " ",
      _toDisplayString(foo),
      _toDisplayString(_ctx.bar),
      _toDisplayString(_ctx.baz)
    ]),
    _: 1 /* STABLE */
  }))
}"
`;

exports[`compiler: transform component slots > on component dynamically named slot 1`] = `
"const { toDisplayString: _toDisplayString, resolveComponent: _resolveComponent, withCtx: _withCtx, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, {
    [_ctx.named]: _withCtx(({ foo }) => [_toDisplayString(foo), _toDisplayString(_ctx.bar)]),
    _: 2 /* DYNAMIC */
  }, 1024 /* DYNAMIC_SLOTS */))
}"
`;

exports[`compiler: transform component slots > on component named slot 1`] = `
"const { toDisplayString: _toDisplayString, resolveComponent: _resolveComponent, withCtx: _withCtx, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, {
    named: _withCtx(({ foo }) => [_toDisplayString(foo), _toDisplayString(_ctx.bar)]),
    _: 1 /* STABLE */
  }))
}"
`;

exports[`compiler: transform component slots > on-component default slot 1`] = `
"const { toDisplayString: _toDisplayString, resolveComponent: _resolveComponent, withCtx: _withCtx, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, {
    default: _withCtx(({ foo }) => [_toDisplayString(foo), _toDisplayString(_ctx.bar)]),
    _: 1 /* STABLE */
  }))
}"
`;

exports[`compiler: transform component slots > template named slots 1`] = `
"const { toDisplayString: _toDisplayString, resolveComponent: _resolveComponent, withCtx: _withCtx, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, {
    one: _withCtx(({ foo }) => [_toDisplayString(foo), _toDisplayString(_ctx.bar)]),
    two: _withCtx(({ bar }) => [_toDisplayString(_ctx.foo), _toDisplayString(bar)]),
    _: 1 /* STABLE */
  }))
}"
`;

exports[`compiler: transform component slots > with whitespace: 'preserve' > implicit default slot 1`] = `
"const { createElementVNode: _createElementVNode, resolveComponent: _resolveComponent, withCtx: _withCtx, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, {
    header: _withCtx(() => [" Header "]),
    default: _withCtx(() => [
      " ",
      _createElementVNode("p")
    ]),
    _: 1 /* STABLE */
  }))
}"
`;

exports[`compiler: transform component slots > with whitespace: 'preserve' > named default slot + implicit whitespace content 1`] = `
"const { resolveComponent: _resolveComponent, withCtx: _withCtx, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, {
    header: _withCtx(() => [" Header "]),
    default: _withCtx(() => [" Default "]),
    _: 1 /* STABLE */
  }))
}"
`;

exports[`compiler: transform component slots > with whitespace: 'preserve' > should not generate whitespace only default slot 1`] = `
"const { resolveComponent: _resolveComponent, withCtx: _withCtx, openBlock: _openBlock, createBlock: _createBlock } = Vue

return function render(_ctx, _cache) {
  const _component_Comp = _resolveComponent("Comp")

  return (_openBlock(), _createBlock(_component_Comp, null, {
    header: _withCtx(() => [" Header "]),
    footer: _withCtx(() => [" Footer "]),
    _: 1 /* STABLE */
  }))
}"
`;
