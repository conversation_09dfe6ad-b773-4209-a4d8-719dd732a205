// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`CSS vars injection > codegen > <script> w/ default export 1`] = `
"const __default__ = { setup() {} }
import { useCssVars as _useCssVars } from 'vue'
const __injectCSSVars__ = () => {
_useCssVars(_ctx => ({
  "xxxxxxxx-color": (_ctx.color)
}))}
const __setup__ = __default__.setup
__default__.setup = __setup__
  ? (props, ctx) => { __injectCSSVars__();return __setup__(props, ctx) }
  : __injectCSSVars__

export default __default__"
`;

exports[`CSS vars injection > codegen > <script> w/ default export in strings/comments 1`] = `
"
          // export default {}
          const __default__ = {}
        
import { useCssVars as _useCssVars } from 'vue'
const __injectCSSVars__ = () => {
_useCssVars(_ctx => ({
  "xxxxxxxx-color": (_ctx.color)
}))}
const __setup__ = __default__.setup
__default__.setup = __setup__
  ? (props, ctx) => { __injectCSSVars__();return __setup__(props, ctx) }
  : __injectCSSVars__

export default __default__"
`;

exports[`CSS vars injection > codegen > <script> w/ no default export 1`] = `
"const a = 1
const __default__ = {}
import { useCssVars as _useCssVars } from 'vue'
const __injectCSSVars__ = () => {
_useCssVars(_ctx => ({
  "xxxxxxxx-color": (_ctx.color)
}))}
const __setup__ = __default__.setup
__default__.setup = __setup__
  ? (props, ctx) => { __injectCSSVars__();return __setup__(props, ctx) }
  : __injectCSSVars__

export default __default__"
`;

exports[`CSS vars injection > codegen > should ignore comments 1`] = `
"import { useCssVars as _useCssVars, unref as _unref } from 'vue'
const color = 'red';const width = 100
export default {
  setup(__props, { expose: __expose }) {
  __expose();

_useCssVars(_ctx => ({
  "xxxxxxxx-width": (width)
}))

return { color, width }
}

}"
`;

exports[`CSS vars injection > codegen > should work with w/ complex expression 1`] = `
"import { useCssVars as _useCssVars, unref as _unref } from 'vue'

export default {
  setup(__props, { expose: __expose }) {
  __expose();

_useCssVars(_ctx => ({
  "xxxxxxxx-foo": (_unref(foo)),
  "xxxxxxxx-foo\\ \\+\\ \\'px\\'": (_unref(foo) + 'px'),
  "xxxxxxxx-\\(a\\ \\+\\ b\\)\\ \\/\\ 2\\ \\+\\ \\'px\\'": ((_unref(a) + _unref(b)) / 2 + 'px'),
  "xxxxxxxx-\\(\\(a\\ \\+\\ b\\)\\)\\ \\/\\ \\(2\\ \\*\\ a\\)": (((_unref(a) + _unref(b))) / (2 * _unref(a)))
}))

        let a = 100
        let b = 200
        let foo = 300
        
return { get a() { return a }, set a(v) { a = v }, get b() { return b }, set b(v) { b = v }, get foo() { return foo }, set foo(v) { foo = v } }
}

}"
`;

exports[`CSS vars injection > codegen > w/ <script setup> 1`] = `
"import { useCssVars as _useCssVars, unref as _unref } from 'vue'
const color = 'red'
export default {
  setup(__props, { expose: __expose }) {
  __expose();

_useCssVars(_ctx => ({
  "xxxxxxxx-color": (color)
}))

return { color }
}

}"
`;

exports[`CSS vars injection > codegen > w/ <script setup> using the same var multiple times 1`] = `
"import { useCssVars as _useCssVars, unref as _unref } from 'vue'
const color = 'red'
        
export default {
  setup(__props, { expose: __expose }) {
  __expose();

_useCssVars(_ctx => ({
  "xxxxxxxx-color": (color)
}))

        
return { color }
}

}"
`;

exports[`CSS vars injection > generating correct code for nested paths 1`] = `
"const a = 1
const __default__ = {}
import { useCssVars as _useCssVars } from 'vue'
const __injectCSSVars__ = () => {
_useCssVars(_ctx => ({
  "xxxxxxxx-color": (_ctx.color),
  "xxxxxxxx-font\\.size": (_ctx.font.size)
}))}
const __setup__ = __default__.setup
__default__.setup = __setup__
  ? (props, ctx) => { __injectCSSVars__();return __setup__(props, ctx) }
  : __injectCSSVars__

export default __default__"
`;

exports[`CSS vars injection > w/ <script setup> binding analysis 1`] = `
"import { useCssVars as _useCssVars, unref as _unref } from 'vue'
import { ref } from 'vue'
        const color = 'red'
        
export default {
  props: {
          foo: String
        },
  setup(__props, { expose: __expose }) {
  __expose();

_useCssVars(_ctx => ({
  "xxxxxxxx-color": (color),
  "xxxxxxxx-size": (size.value),
  "xxxxxxxx-foo": (__props.foo)
}))

        const size = ref('10px')
        
        
return { color, size, ref }
}

}"
`;

exports[`CSS vars injection > w/ normal <script> binding analysis 1`] = `
"
      const __default__ = {
        setup() {
          return {
            size: ref('100px')
          }
        }
      }
      
import { useCssVars as _useCssVars } from 'vue'
const __injectCSSVars__ = () => {
_useCssVars(_ctx => ({
  "xxxxxxxx-size": (_ctx.size)
}))}
const __setup__ = __default__.setup
__default__.setup = __setup__
  ? (props, ctx) => { __injectCSSVars__();return __setup__(props, ctx) }
  : __injectCSSVars__

export default __default__"
`;
