{"name": "@vue/runtime-test", "private": true, "version": "0.0.0", "description": "@vue/runtime-test", "main": "index.js", "module": "dist/runtime-test.esm-bundler.js", "types": "dist/runtime-test.d.ts", "files": ["index.js", "dist"], "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/runtime-test"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/runtime-test#readme", "dependencies": {"@vue/shared": "workspace:*", "@vue/runtime-core": "workspace:*"}}